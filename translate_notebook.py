#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def translate_text(text):
    """翻译文本内容，保持代码不变"""
    
    # 翻译映射字典
    translations = {
        # 基本术语
        "Introduction to Python": "Python 入门",
        "Notebook introduction": "笔记本介绍", 
        "Introduction to Python programming": "Python编程入门",
        "Programming with Python": "使用Python编程",
        "Basic concepts": "基本概念",
        "Variables": "变量",
        "Control structures": "控制结构",
        "Data structures": "数据结构", 
        "Syntax": "语法",
        "Strings": "字符串",
        "Integers": "整数",
        "Booleans": "布尔值",
        "Floats": "浮点数",
        "Complex numbers": "复数",
        "Assignment operator and variables": "赋值运算符和变量",
        "Aggregate data types: Lists and tuples": "聚合数据类型：列表和元组",
        "Lists": "列表",
        "Tuples": "元组",
        "Loops, sequences, and conditional statements": "循环、序列和条件语句",
        "Range": "范围",
        "Basic loops": "基本循环",
        "Conditional statements": "条件语句",
        "Functions": "函数",
        "Graphics and visualization": "图形和可视化",
        "Matplotlib": "Matplotlib",
        "Simple plot": "简单绘图",
        "Saving your notebook": "保存您的笔记本",
        "Additional resources": "其他资源",
        "References": "参考文献",
        
        # 长句翻译
        "This section will serve as a summarized version of useful commands that are needed to get non-technical users started, and equip them with the basics required to complete this course.": "本节将作为有用命令的总结版本，这些命令是让非技术用户入门所需的，并为他们提供完成本课程所需的基础知识。",
        
        "This course is not intended to be a Python training course, but rather to showcase how Python can be utilized in analysis projects. You will be provided with links that you can explore in your own time in order to further your expertise.": "本课程不是Python培训课程，而是展示如何在分析项目中使用Python。您将获得可以在自己的时间内探索的链接，以进一步提高您的专业知识。",
        
        "You should execute the cells with sample code and write your own code in the indicated cells in this notebook. When complete, ensure that you save and checkpoint the notebook, download a copy to your local workstation, and submit a copy to the Online Campus.": "您应该执行包含示例代码的单元格，并在本笔记本中指定的单元格中编写自己的代码。完成后，确保保存并检查点笔记本，下载副本到您的本地工作站，并提交副本到在线校园。",
        
        "You can also visit the [Python Language Reference](https://docs.python.org/3/reference/index.html) for a more complete reference manual that describes the syntax and \\\"core semantics\\\" of the language.": "您也可以访问[Python语言参考](https://docs.python.org/3/reference/index.html)获取更完整的参考手册，描述语言的语法和\\\"核心语义\\\"。",
        
        "> **Note to new Python users**:": "> **给Python新用户的注意事项**：",
        
        "> This notebook will introduce a significant amount of new content. You do not need to be able to master all of the components, but you are urged to work through the examples below, and to attempt following the logic. In this notebook, you will be introduced to Python syntax. The second notebook in this module will start to introduce various components of data analysis basics, and the third will guide you through a basic example from beginning to end. The focus will shift from programming to subject-related examples in subsequent modules.": "> 本笔记本将介绍大量新内容。您不需要能够掌握所有组件，但我们敦促您通过下面的示例，并尝试跟随逻辑。在本笔记本中，您将了解Python语法。本模块中的第二个笔记本将开始介绍数据分析基础的各种组件，第三个将指导您从头到尾完成一个基本示例。在后续模块中，重点将从编程转向与主题相关的示例。",
        
        # 注释翻译
        "# I am a comment and I'm ignored by the interpreter.": "# 我是一个注释，解释器会忽略我。",
        "# A valid value of type string that can be manipulated.": "# 可以操作的字符串类型的有效值。",
        "# Alternatively, you can also use single quotes to specify the value.": "# 或者，您也可以使用单引号来指定值。",
        "# Find the type of a valid value that can be manipulated.": "# 查找可以操作的有效值的类型。",
        "# String concatenation using the '+' operator.": "# 使用'+'运算符进行字符串连接。",
        "# How long is the string 'Bob'.": "# 字符串'Bob'有多长。",
        "# Print 'Bob' 3 times with no space.": "# 打印'Bob' 3次，没有空格。",
        "# Get the 1st element of the string 'Bob'.": "# 获取字符串'Bob'的第1个元素。",
        "# Get the second element of the string 'Bob'.": "# 获取字符串'Bob'的第二个元素。",
        "# Get the third element of the string 'Bob'.": "# 获取字符串'Bob'的第三个元素。",
        "# Get the last element of the string 'Bob'.": "# 获取字符串'Bob'的最后一个元素。",
        "# Get the second last element of the string 'Bob'.": "# 获取字符串'Bob'的倒数第二个元素。",
        "# Slicing: Remove the last element of the string 'BobBobBob' and return the remainder of the string.": "# 切片：删除字符串'BobBobBob'的最后一个元素并返回字符串的其余部分。",
        
        # 更多翻译...
        "Another example of a value is Bob's age. Assuming Bob is 40 years old, the value can be specified as follows:": "另一个值的例子是Bob的年龄。假设Bob是40岁，该值可以指定如下：",
        "# A valid value of type integer.": "# 整数类型的有效值。",
        "# The type of the valid value for Bob's age.": "# Bob年龄的有效值的类型。",
        
        # 警告框翻译
        "It is strongly recommended that you save and checkpoint after applying significant changes or completing exercises. This allows you to return the notebook to a previous state should you wish to do so. On the Jupyter menu, select \\\"File\\\", then \\\"Save and Checkpoint\\\" from the dropdown menu that appears.": "强烈建议您在应用重大更改或完成练习后保存并检查点。这允许您在希望时将笔记本返回到以前的状态。在Jupyter菜单上，选择\\\"文件\\\"，然后从出现的下拉菜单中选择\\\"保存并检查点\\\"。",
    }
    
    # 应用翻译
    result = text
    for en, zh in translations.items():
        result = result.replace(en, zh)
    
    return result

def main():
    # 读取文件
    with open('Python入门课程.ipynb', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 翻译所有markdown单元格
    for cell in data['cells']:
        if cell['cell_type'] == 'markdown':
            for i, line in enumerate(cell['source']):
                cell['source'][i] = translate_text(line)
    
    # 保存翻译后的文件
    with open('Python入门课程.ipynb', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=1)
    
    print("翻译完成！")

if __name__ == "__main__":
    main()
