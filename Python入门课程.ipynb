{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<div align=\"right\">Python 3.6 Jupyter Notebook</div>"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Python 入门"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 完成本笔记本练习将使您能够做到以下几点：\n",
    "\n",
    "> **理解**：您的伪代码和注释是否显示出您回忆和理解技术概念的证据？\n",
    "\n",
    "> **应用**：您是否能够执行代码（使用提供的示例），在提供的或生成的数据集上执行所需的功能？"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "#### 笔记本目标\n",
    "在本笔记本结束时，您应该能够：\n",
    "> \n",
    "  - 识别**编程的基本概念**；\n",
    "  - 理解**Python语法**；\n",
    "  - 区分Python中使用的不同**原生数据类型**；\n",
    "  - 使用**流程控制结构**来实现**条件和重复逻辑**；\n",
    "  - 理解**函数**作为一个有组织的、可重用的代码块，用于执行单一的相关操作，对于提高应用程序的模块化以及允许在应用程序内部和跨应用程序的代码重用是必要的；以及\n",
    "  - 理解Python**包**作为一个包含Python代码的文件，定义函数、类、变量，也可能包含可运行的代码。您应该能够**导入**Python包或从包中导入特定函数。\n",
    " \n",
    "####  练习列表\n",
    ">   - 练习1：打印字符串。\n",
    "  - 练习2：掷骰子。\n",
    "  - 练习3：硬币翻转分布。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 笔记本介绍\n",
    "\n",
    "本节将作为有用命令的总结版本，这些命令是让非技术用户入门所需的，并为他们提供完成本课程所需的基础知识。\n",
    "\n",
    "本课程不是Python培训课程，而是展示如何在分析项目中使用Python。您将获得可以在自己的时间内探索的链接，以进一步提高您的专业知识。\n",
    "\n",
    "您应该执行包含示例代码的单元格，并在本笔记本中指定的单元格中编写自己的代码。完成后，确保保存并检查点笔记本，下载副本到您的本地工作站，并提交副本到在线校园。\n",
    "\n",
    "您也可以访问[Python语言参考](https://docs.python.org/3/reference/index.html)获取更完整的参考手册，描述语言的语法和"核心语义"。\n",
    "\n",
    "> **给Python新用户的注意事项**：\n",
    "\n",
    "> 本笔记本将介绍大量新内容。您不需要能够掌握所有组件，但我们敦促您通过下面的示例，并尝试跟随逻辑。在本笔记本中，您将了解Python语法。本模块中的第二个笔记本将开始介绍数据分析基础的各种组件，第三个将指导您从头到尾完成一个基本示例。在后续模块中，重点将从编程转向与主题相关的示例。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<div class=\"alert alert-warning\">\n",
    "<b>注意</b>：<br>\n",
    "强烈建议您在应用重大更改或完成练习后保存并检查点。这允许您在希望时将笔记本返回到以前的状态。在Jupyter菜单上，选择"文件"，然后从出现的下拉菜单中选择"保存并检查点"。\n",
    "</div>"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 1. Python编程入门"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "Python通常被[定义](https://docs.python.org/3/faq/general.html#what-is-python)为*高级、通用、解释型、动态计算机编程语言*。\n",
    "\n",
    "让我们尝试为编程新手分解这个陈述。高级意味着我们可以用更接近人类语言的语言表达计算机能够执行的指令，而不是计算机运行指令所需的机器语言。解释器是一个将高级编程指令转换为计算机理解的低级编程指令的程序。动态意味着它们不在"编译时"强制执行或检查类型安全，而是将这些检查推迟到"运行时"。这有许多优点，包括较低的开发成本、快速原型制作以及特定领域（如数据处理和分析）所需的灵活性。这使Python成为本课程的完美语言，因为它易于理解（使用的表达式与日常对话非常相似），语法简单，并提供适合数据处理和分析的对象。\n",
    "\n",
    "因为它是一种编程语言，在开始编码之前，有一些基本的计算机科学概念很重要理解。\n",
    "\n",
    "## 1.1 基本概念\n",
    "\n",
    "### 1.1.1 变量\n",
    "变量是任何程序的支柱，因此也是任何编程语言的支柱。变量用于存储信息，以便在通常称为计算机程序的一组指令中引用和操作。变量还提供了一种用描述性名称标记数据的方法，以便程序能够被读者和我们自己更清楚地理解（回想上面的高级）。将变量视为保存信息的容器是有帮助的。它们的唯一目的是在计算机内存中标记和存储数据。然后可以在整个程序中使用这些数据。\n",
    "\n",
    "变量可以存储各种信息，如人的姓名、该人的年龄或体重，或他们的出生日期。这些信息中的每一个都包含不同类型的信息。包含人名的变量的数据类型将是字符串，而年龄存储为整数，出生日期作为日期时间对象。幸运的是，在Python中，与Java或C不同，您不需要在将对象分配给变量之前告诉计算机变量是什么类型。这意味着您可以通过将您的姓名（比如说）称为"X"来开始您的程序。\n",
    "\n",
    "    >>> X = 'Mary'\n",
    "    \n",
    "在程序中间，您可能会改变主意，并将您的"X"变量重新分配给您的年龄。\n",
    "\n",
    "    >>> X = 21\n",
    "\n",
    "这为您提供了灵活性，您可以利用这种灵活性快速测试想法，而不必担心代码在执行时会中断。与任何事情一样，"能力越大，责任越大！"最佳实践是使用对变量存储的数据直观的名称。唯一的例外是使用编程语言保留的名称，称为**关键字**。关键字定义语言的规则和结构，不能用作变量名。在Jupyter内部，Python保留名称的高亮显示与其他名称不同，这很快就会变得清楚。\n",
    "\n",
    "变量的类型还定义了您可以做什么，以及当您对其执行某些操作时可以期望的行为。这在本笔记本后面有说明。\n",
    "\n",
    "### 1.1.2 控制结构\n",
    "当我们将一组指令放在一起供计算机执行时，计算机按顺序读取指令。这被称为"代码流"。然而，代码中的指令经常需要做出决定。例如，假设您编写了一个程序来接受实习生到您的组织。为了遵守管理您组织的劳动法规，您要求每个候选人输入他们的年龄以及其他详细信息。在接受他们作为实习生之前，您的程序检查该人的年龄是否在规定的限制内。如果不是，潜在实习生的申请将被拒绝。这种影响代码流的决策点在计算机编程中经常出现，被称为**流程控制结构**。[具体来说](https://ocw.mit.edu/courses/electrical-engineering-and-computer-science/6-096-introduction-to-c-january-iap-2011/lecture-notes/MIT6_096IAP11_lec02.pdf)：\n",
    "\n",
    "> 控制结构是一个编程块，它分析变量并根据给定的参数选择前进的方向。术语流程控制详细说明了程序采取的方向（程序控制"流动"的方式）。因此，它是计算中的基本决策过程；流程控制确定计算机在给定某些条件和参数时如何响应。\n",
    "\n",
    "控制结构使程序正常运行，没有控制结构，您的程序代码只会从上到下线性流动。根据变量的值改变程序采取的操作，这就是编程有用的原因。下面，您将了解Python中可用的不同控制结构。\n",
    "\n",
    "### 1.1.3 数据结构\n",
    "数据结构是在计算机中存储和组织数据的特定方式，以便可以有效地使用它。之前，您使用人的姓名、年龄和出生日期作为示例介绍了变量的概念。想象一下为注册MIT大数据和社会分析证书课程的每个人都这样做。那将是很多变量，您将很难跟踪。数据结构是一种避免必须创建数百万个变量的方法。Python实现了许多您将使用的数据结构，如列表、字典、元组和序列。此外，可以在这些基础上构建其他数据结构，以便有效地处理数据。在Python中，NumPy和Pandas模块提供的数据结构对于我们在数据分析中的许多需求来说是直观和富有表现力的。\n",
    "\n",
    "### 1.1.4 语法\n",
    "每种编程语言的设计和实现都不同，使用它需要遵守其语法，语法是定义在该语言中被认为是正确结构程序的符号组合规则的集合。当遵循这些规则时，编程语言可以理解您的指令，因此您能够创建可以运行的软件。如果您不遵守编程语言语法的规则，您将不幸遇到错误，您的程序将不会执行。事实证明，任何编程语言的语法往往是许多人在第一次接触编程或学习新编程语言时面临的最大障碍。（请注意，语法错误只是许多可能错误中的一种类型。）幸运的是，Python会在遇到语法错误时正确识别它们。这在尝试查找代码中的错误时很有用。使用Python时要记住的一些关键事项如下所示。\n",
    "\n",
    "**大小写敏感性**\n",
    "\n",
    "> 所有变量都区分大小写。Python将"number"和"Number"视为单独的、不相关的实体。\n",
    "\n",
    " \n",
    "**注释**\n",
    "> 注释行以井号符号<font color='brown'> # </font>开头，在运行时解释代码时被忽略。\n",
    "\n",
    "**缩进**\n",
    "> 代码块通过缩进来划分。特别是，**控制结构**在决策点以冒号<font color='brown'> : </font>结尾。指示当决策评估为"True"时必须采取的操作的代码块通过缩进来划分。\n",
    ">> 注意：**空格和制表符不能混合**\n",
    "因为空白很重要，请记住空格和制表符不能混合，所以在缩进程序时只使用其中一种。一个常见的错误是将它们混淆。虽然它们在编辑时可能看起来相同，但解释器会以不同的方式读取它们，这将导致错误或意外行为。大多数好的文本编辑器可以配置为让制表键发出空格而不是制表符。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "总之，本节描述了什么是变量，如何在其中存储信息，然后在稍后阶段检索该信息。变量可以有一个名称，这个名称通常由您将在变量中存储的内容类型决定。因此，如果您在变量中存储您的姓名，您会将变量命名为"yourName"。您不**必须**给它那个名称，您可以将变量命名为"WowImProgramming"，但考虑到您试图存储一个人的姓名，这没有多大意义。最后，变量有类型，这些类型用于帮助我们组织什么可以和不能存储在变量中。\n",
    "\n",
    "> **提示**：拥有类型将有助于开放我们可以对变量内信息做什么样的事情。\n",
    "\n",
    "\n",
    "> **示例**：如果您有两个整数（比如说50和32），您将能够从另一个变量中减去一个变量（即50 – 32 = 18）。但是，如果您有两个存储姓名的变量（例如，"Trevor"和"Geoff"），从另一个中减去一个就没有意义（即"Trevor" – "Geoff"），因为这没有任何意义。\n",
    "\n",
    "因此，类型是一个强大的东西，它们帮助您理解您**可以**和不能对变量做什么。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 2. 使用Python编程\n",
    "\n",
    "现在基本概念已经介绍，让我们使用说明性示例使这些具体化。本笔记本使用Jupyter的代码单元格（浅灰色背景）来演示这些想法。正如您在定向模块中学到的，您可以在代码单元格中执行指令，输出将显示在单元格下方（如果适用）。\n",
    "\n",
    "在大多数情况下，单元格的第一行包含注释以帮助跟随材料。注释是指令集中以井号符号开头的任何行。在Jupyter内部，一旦您使用了注释指示符，该行就会以不同的颜色突出显示并且字体变为斜体，这对读者和开发人员来说都是非常有用的视觉辅助。请参见下面的示例：\n",
    "\n",
    "> **注意**：\n",
    "\n",
    "> 下面的代码单元格在执行时不会产生任何输出。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 1,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 我是一个注释，解释器会忽略我。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.1 字符串"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "Python处理可以根据数据类型采用不同形式的"值"。例如，人的名字和年龄都构成Python内部不同的有效值。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 2,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'Bob'"
      ]
     },
     "execution_count": 2,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 可以操作的字符串类型的有效值。\n",
    "\"Bob\""
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 3,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'Bob'"
      ]
     },
     "execution_count": 3,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 或者，您也可以使用单引号来指定值。\n",
    "'Bob'"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "您可以使用函数"``type()``"并在括号之间包含值的名称来找出值是什么类型。（Python中的函数在本笔记本后面有更详细的解释。）"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 4,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "str"
      ]
     },
     "execution_count": 4,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 查找可以操作的有效值的类型。\n",
    "type('Bob')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "从上面可以看出，我们的值```'Bob'```的类型是```str```。字符串数据类型的值是字符序列。字符串通常用于文本处理。\n",
    "您可以对字符串执行连接和许多其他函数。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 5,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'He is Bob'"
      ]
     },
     "execution_count": 5,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 使用'+'运算符进行字符串连接。\n",
    "'He' + ' ' + 'is' + ' ' + 'Bob'"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 6,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 字符串'Bob'有多长。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 7,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "3"
      ]
     },
     "execution_count": 7,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "len('Bob')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 8,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 打印'Bob' 3次，没有空格。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 9,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'BobBobBob'"
      ]
     },
     "execution_count": 9,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "'Bob'*3"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 10,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'B'"
      ]
     },
     "execution_count": 10,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 获取字符串'Bob'的第1个元素。\n",
    "'Bob'[0]"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 11,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'o'"
      ]
     },
     "execution_count": 11,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 获取字符串'Bob'的第二个元素。\n",
    "'Bob'[1]"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 12,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'b'"
      ]
     },
     "execution_count": 12,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 获取字符串'Bob'的第三个元素。\n",
    "'Bob'[2]"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 13,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'b'"
      ]
     },
     "execution_count": 13,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 获取字符串'Bob'的最后一个元素。\n",
    "'Bob'[-1]"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 14,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'o'"
      ]
     },
     "execution_count": 14,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 获取字符串'Bob'的倒数第二个元素。\n",
    "'Bob'[-2]"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 15,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'BobBobBo'"
      ]
     },
     "execution_count": 15,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 切片：删除字符串'BobBobBob'的最后一个元素并返回字符串的其余部分。\n",
    "('Bob'*3)[0:-1]"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.2 整数"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "另一个值的例子是Bob的年龄。假设Bob是40岁，该值可以指定如下："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 16,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "40"
      ]
     },
     "execution_count": 16,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 整数类型的有效值。\n",
    "40"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 17,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "int"
      ]
     },
     "execution_count": 17,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# Bob年龄的有效值的类型。\n",
    "type(40)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "注意两个值指定方式的差异：对于字符串值，名称包含在单引号或双引号之间，而年龄值不需要这样。如果在后者中使用了引号，它将表达不同类型的不同值。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 18,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "'40'"
      ]
     },
     "execution_count": 18,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 一个有效值，但不是整数类型。\n",
    "'40'"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.3 布尔值"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "让我们通过这个例子介绍三个新想法：如何比较两个值，Python中的另一个有效数据类型，以及类型转换的概念。首先，让我们比较两个值（40和'40'）看看它们是否是同一类型。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 19,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "False"
      ]
     },
     "execution_count": 19,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "type(40) == type('40')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "这里发生了什么？我们使用了一个运算符（```==```），它接受两边的一个值，并比较这些值看看它们是否相同。在这种情况下，值不是```40```和```'40'```，而是它们的值类型（即整数、字符串等）。（如果我们的意图是比较原始值，我们会使用```40 == '40'```）\n",
    "\n",
    "结果的False值是什么类型？"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 20,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "bool"
      ]
     },
     "execution_count": 20,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 比较结果值的类型。\n",
    "type(type(40) == type('40'))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    ""False"值是"布尔"类型，这是编程中的另一个基本数据类型。布尔值只能取两个值："True"或"False"。\n",
    "\n",
    "通常情况下，您不想使用原生数据类型（如您刚刚看到的布尔数据类型），而是想使用不同但等效的数据类型。例如，如果您接受"False"值在给定上下文中表示缺少某些东西，您可能想将其表示为值为0的整数。这是使用**类型转换**的概念实现的，它允许某些数据类型被转换为不同的类型。在Python中，您通过调用要转换到的类型，并将值作为该调用的参数来转换值。因此，在这种情况下，您实现以下操作。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 21,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "0"
      ]
     },
     "execution_count": 21,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 将布尔值转换为整数。\n",
    "int(type(40) == type('40'))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.4 浮点数"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "浮点数是包含浮动小数点的数字。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 22,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "2.6"
      ]
     },
     "execution_count": 22,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 浮点数的示例。\n",
    "2.6"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 23,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "float"
      ]
     },
     "execution_count": 23,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 查找浮点数的类型。\n",
    "type(2.6)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "记住，数据类型定义了相同类型值之间允许的操作。但是，某些数据类型（如整数和浮点数）可以在表达式中使用。在这种情况下，类型转换会自动发生，无需用户手动执行。结果值采用所涉及值中更包容的数据类型的类型，如下例所示："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 24,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "42.6"
      ]
     },
     "execution_count": 24,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 将整数加到浮点数会得到浮点类型的值。\n",
    "40 + 2.6"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 25,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "float"
      ]
     },
     "execution_count": 25,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "type(40 + 2.6)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "当除以两个整数值时会发生什么？"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 26,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "0.275"
      ]
     },
     "execution_count": 26,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 除以两个整数。\n",
    "11 / 40"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 27,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "float"
      ]
     },
     "execution_count": 27,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 两个整数相除得到的值的类型。\n",
    "type(11 / 40)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<div class=\"alert alert-warning\">\n",
    "<b>Python 2.7用户注意</b>：<br>\n",
    "事实证明，在Python 2.7和更早版本中，涉及两个整数的操作将导致整数值，即使正确的结果是浮点数。因此，在处理整数操作时需要非常小心。理想情况下，您总是希望确保将其中一个值（无论哪个）转换为浮点数，以便结果值正确表示为浮点数。Python 3及更高版本不需要这样做。\n",
    "</div>"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 28,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Python 2.7及更早版本执行数学运算前转换整数的示例。\n",
    "# 11/float(40)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.5 复数"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "复数在许多科学和工程学科中很重要。Python也可以表示和执行[复数](https://docs.python.org/3/library/cmath.html)计算。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 29,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "(1.5+2j)"
      ]
     },
     "execution_count": 29,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "# 复数由实部+虚部表示。虚部通过添加后缀j来表示。\n",
    "1.5 + 2j"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 30,
   "metadata": {},
   "outputs": [
    {
     "data": {
      "text/plain": [
       "complex"
      ]
     },
     "execution_count": 30,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "type((1.5 + 2j))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.6 赋值运算符和变量"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "我们的表达式，例如\"```type(40) == type('40')```\"，可能很快变得笨拙并且成为难以调试的错误源。为了帮助优雅地（和程序化地）操作值，通常使用变量来存储感兴趣的值。然后通过引用变量来引用该值。要将值分配给变量，我们使用赋值运算符\"```=```\"，如下例所示："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 31,
   "metadata": {},
   "outputs": [],
   "source": [
    "a_boolean_value = (type(40) == type('40'))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "这里，赋值运算符"```=```"右侧的值已分配给巧妙命名为"```a_boolean_value```"的变量。您现在可以在代码的其他部分使用该变量。\n",
    "\n",
    "让我们介绍另一个函数，称为"print"（另一个**关键字**），它打印与您的变量关联的值。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 32,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "False\n"
     ]
    }
   ],
   "source": [
    "print(a_boolean_value)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "您也可以对之前遇到的其他表达式使用print："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 33,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "0.275\n"
     ]
    }
   ],
   "source": [
    "print(11/float(40))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "print函数允许您执行其他格式化和有趣的值引用。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 34,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Bob is 40 years old and spent 11 in school, that is 27.5% of his life.\n"
     ]
    }
   ],
   "source": [
    "print('{} is {} years old and spent {} in school, that is {}% of his life.'.format('Bob',40, 11,100*float(11)/40))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<br>\n",
    "<div class=\"alert alert-info\">\n",
    "<b>练习1开始：打印字符串。</b>\n",
    "</div>\n",
    "\n",
    "### 说明\n",
    "1. 将您的名字和姓氏设置为单独的变量（分别为\"firstname\"和\"lastname\"）。\n",
    "2. 使用上面讨论的\"``str.format()``\"形式打印您的名字和姓氏。在打印输出中的名字和姓氏之间包含一个空格字符。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 37,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Davide La Torre\n"
     ]
    }
   ],
   "source": [
    "firstname = 'Davide'\n",
    "lastname = 'La Torre'\n",
    "print('{} {}' .format(firstname,lastname))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<br>\n",
    "<div class=\"alert alert-info\">\n",
    "<b>练习1结束。</b>\n",
    "</div>\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "> **练习完成**：\n",
    "    \n",
    "> 这是一个"保存并检查点"的好时机。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.7 聚合数据类型：列表和元组\n",
    "\n",
    "变量存储可能随时间变化的信息。当您需要存储更长的信息列表时，有其他可用选项。您将了解列表和元组作为基本元素，并可以在[Python文档](https://docs.python.org/3/tutorial/datastructures.html)中阅读有关原生Python数据结构的更多信息。在本课程中，您将接触到使用[Pandas](http://pandas.pydata.org/)和[NumPy](http://www.numpy.org/)库的示例，这些库为数据分析和操作提供了额外的选项。\n",
    "\n",
    "### 2.7.1 列表\n",
    "[列表](https://docs.python.org/3/tutorial/introduction.html#lists)是可变的值序列。它们存储在方括号内，列表中的项目用逗号分隔。列表也可以被修改、追加和排序，以及其他[方法](https://docs.python.org/3/tutorial/datastructures.html#more-on-lists)。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "#### 创建和打印列表"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 38,
   "metadata": {
    "scrolled": true
   },
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "['this', 'is', 'a', 'list']\n",
      "The list contains 4 elements.\n"
     ]
    }
   ],
   "source": [
    "# 列表。\n",
    "lst = ['this', 'is', 'a', 'list']\n",
    "print(lst)\n",
    "\n",
    "# 打印列表中元素的数量。\n",
    "print('The list contains {} elements.'.format(len(lst)))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.8 循环、序列和条件语句\n",
    "条件和循环是控制结构，可用于根据指定的条件或循环重复语句。这可以用于循环遍历数据集或对输入变量执行（或基于）一系列步骤。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.9 范围\n",
    "[Range](https://docs.python.org/3/library/stdtypes.html#range)（开始，停止，步长）用于创建包含算术级数的列表。如果您只用一个参数调用range，它将使用该值作为停止值，默认从零开始。步长参数是可选的，可以是正整数或负整数。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 生成10个值的列表。\n",
    "myrange = list(range(10))\n",
    "myrange"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.10 基本循环\n",
    "Python使用缩进来重复项目。下面的示例演示如何执行以下操作：\n",
    "- 循环遍历生成的列表并：\n",
    "  - 打印列表中的当前元素；以及\n",
    "  - 打印每个元素重复的X。\n",
    "- 退出循环并打印不重复的行。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 您可以手动指定列表或使用包含列表的变量作为输入。\n",
    "# 手动输入的语法是：`for item in [1, 2, 3]:`\n",
    "\n",
    "for item in myrange:\n",
    "    print(item)\n",
    "    print(item * 'X')\n",
    "print('End of loop (not included in the loop)')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.11 条件语句\n",
    "条件语句用于确定执行哪些语句。在下面的示例中，我们导入"random"库并生成一个小于2的随机数。假设0表示正面，1表示反面。然后使用条件语句打印硬币翻转的结果，以及"正面"或"反面"。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 翻硬币。\n",
    "\n",
    "import random\n",
    "coin_result = random.randrange(0,2)\n",
    "\n",
    "print(coin_result)\n",
    "\n",
    "if coin_result == 0:\n",
    "    print('Heads')\n",
    "else:\n",
    "    print('Tails')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2.12 函数\n",
    "函数允许在应用程序内部和跨应用程序重用代码。定义函数时，您将使用"```def```"语句。然后将所需的函数代码放入语句的主体中。函数通常接受参数并根据函数主体中的代码返回值。在函数主体本身之外引用函数时，此操作称为函数调用。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 带参数'x'的函数'myfirstfunction'。\n",
    "def myfirstfunction(x):\n",
    "    y = x * 6\n",
    "    return y\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "您现在可以如下例所示调用函数。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 调用您的函数。   \n",
    "z = myfirstfunction(6)\n",
    "print(z)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<br>\n",
    "<div class=\"alert alert-info\">\n",
    "<b>练习2开始：掷骰子。</b>\n",
    "</div>\n",
    "\n",
    "### 说明\n",
    "\n",
    "> 使用上面的"coinflip"示例并将其更改为模拟掷骰子。您的输出应包含掷骰子次数的汇总统计信息，以及6个面中每个面的出现次数。\n",
    "\n",
    "> **提示**：\n",
    "\n",
    "> - 用六个新变量替换"Heads"和"Tails"："Side_1"、"Side_2"、"Side_3"等。\n",
    "> - 用```random.randrange(1,7)```替换```random.randrange(0,2)```\n",
    "> - 测试随机变量的每个状态（掷骰子后出现哪一面），并增加相关变量的计数器。\n",
    "> - 将每个变量的最终状态作为向量返回（即[Side_1, Side_2, Side_3, 等]）。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import random\n",
    "Side_1 = 0\n",
    "Side_2 = 0\n",
    "Side_3 = 0\n",
    "Side_4 = 0\n",
    "Side_5 = 0\n",
    "Side_6 = 0\n",
    "current_number_of_flips = 0\n",
    "total_number_of_flips = 60000\n",
    "while current_number_of_flips < total_number_of_flips:\n",
    "    current_flip = random.randrange(1,7)\n",
    "    if current_flip == 1:\n",
    "        Side_1 = Side_1 + 1\n",
    "    if current_flip == 2:\n",
    "        Side_2 = Side_2 + 1\n",
    "    if current_flip == 3:\n",
    "        Side_3 = Side_3 + 1\n",
    "    if current_flip == 4:\n",
    "        Side_4 = Side_4 + 1\n",
    "    if current_flip == 5:\n",
    "        Side_5 = Side_5 + 1\n",
    "    if current_flip == 6:\n",
    "        Side_6 = Side_6 + 1\n",
    "    current_number_of_flips += 1\n",
    "print(Side_1,Side_2,Side_3,Side_4,Side_5,Side_6) "
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<br>\n",
    "<div class=\"alert alert-info\">\n",
    "<b>练习2结束。</b>\n",
    "</div>\n",
    "\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 3. 图形和可视化\n",
    "\n",
    "Matplotlib是Python中用于显示图形和绘图的标准库。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3.1 Matplotlib\n",
    "\n",
    "下面的示例来自[Matplotlib截图](http://matplotlib.org/users/screenshots.html)页面。\n",
    "\n",
    "> **注意**：\n",
    "\n",
    "> 使用Matplotlib时需要记住设置的选项之一是"%matplotlib inline"，它指示笔记本内联绘图，而不是为您的图形打开单独的窗口。\n",
    "\n",
    "您可以通过以下资源找到有关Matplotlib的其他信息：\n",
    "- [Matplotlib文档](http://matplotlib.org/)\n",
    "\n",
    "- [Matplotlib初学者](http://matplotlib.org/users/beginner.html)\n",
    "\n",
    "- [Matplotlib画廊](http://matplotlib.org/gallery.html)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {
    "collapsed": true
   },
   "source": [
    "### 3.1.1 简单绘图\n",
    "下面的图形除了演示绘制图形有多容易之外，没有任何实际意义。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 导入matplotlib库并设置笔记本绘图选项。\n",
    "import matplotlib.pyplot as plt\n",
    "import numpy as np\n",
    "# 指示笔记本内联绘图。\n",
    "%matplotlib inline     \n",
    "plt.rcParams['figure.figsize'] = (15, 9) \n",
    "plt.rcParams['axes.titlesize'] = 'large'\n",
    "\n",
    "# 生成数据。\n",
    "t = np.arange(0.0, 2.0, 0.01)\n",
    "s = np.sin(2*np.pi*t)\n",
    "\n",
    "# 创建绘图。\n",
    "plt.plot(t, s)\n",
    "\n",
    "# 设置绘图选项。\n",
    "plt.xlabel('time (s)')\n",
    "plt.ylabel('voltage (mV)')\n",
    "plt.title('About as simple as it gets, folks')\n",
    "plt.grid(True)\n",
    "\n",
    "# 通过取消注释下面的行可以保存为文件。\n",
    "# plt.savefig(\"test.png\")\n",
    "\n",
    "# 在笔记本中显示绘图。\n",
    "# 之前设置的'%matplotlib inline'选项确保绘图内联显示。\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 4. 保存您的笔记本\n",
    "\n",
    "请确保您：\n",
    "- 执行最终的"保存并检查点"；\n",
    "- 使用"文件"、"下载为"和"IPython Notebook (.ipynb)"将笔记本的副本以".ipynb"格式下载到您的本地机器。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {
    "collapsed": true
   },
   "source": [
    "# 5. 其他资源\n",
    "\n",
    "> **注意**：\n",
    "\n",
    "> 此资源是可选的，仅为希望在Python中完成其他练习的学生提供。\n",
    "\n",
    "- 其他[Python课程](http://www.python-course.eu/course.php)。\n",
    "- [Python初学者技巧](http://www.techbeamers.com/top-10-python-coding-tips-for-beginners/)\n",
    "- [30个基本Python技巧和窍门](http://www.techbeamers.com/essential-python-tips-tricks-programmers/)\n",
    "\n",
    "    "
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {
    "collapsed": true
   },
   "source": [
    "# 6. 参考文献\n",
    "Python Software Foundation. 2016. "General Python FAQ - Python 3.6.2 documentation."最后访问时间：2017年8月20日。https://docs.python.org/3/faq/general.html#what-is-python。\n",
    "\n",
    "MIT OpenCourseWare. 2011. "6.096 Introduction to C++." 访问时间：9月20日。https://ocw.mit.edu/courses/electrical-engineering-and-computer-science/6-096-introduction-to-c-january-iap-2011/lecture-notes/MIT6_096IAP11_lec02.pdf。"
   ]
  }
 ],
 "metadata": {
  "anaconda-cloud": {},
  "kernelspec": {
   "display_name": "Python 3 (ipykernel)",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.9.7"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 1
}
