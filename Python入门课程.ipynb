# I am a comment and I'm ignored by the interpreter.

# A valid value of type string that can be manipulated.
"Bob"

# Alternatively, you can also use single quotes to specify the value.
'<PERSON>'

# Find the type of a valid value that can be manipulated.
type('<PERSON>')

# String concatenation using the '+' operator.
'He' + ' ' + 'is' + ' ' + '<PERSON>'

# How long is the string '<PERSON>'.

len('<PERSON>')

# Print '<PERSON>' 3 times with no space.

'<PERSON>'*3

# Get the 1st element of the string '<PERSON>'.
'<PERSON>'[0]

# Get the second element of the string '<PERSON>'.
'<PERSON>'[1]

# Get the third element of the string '<PERSON>'.
'<PERSON>'[2]

# Get the last element of the string '<PERSON>'.
'<PERSON>'[-1]

# Get the second last element of the string '<PERSON>'.
'<PERSON>'[-2]

# Slicing: Remove the last element of the string '<PERSON><PERSON><PERSON><PERSON><PERSON>' and return the remainder of the string. 
('<PERSON>'*3)[0:-1]

# A valid value of type integer. 
40

# Type of a valid value for <PERSON>'s age.
type(40)

# A valid value but not of type integer.
'40'

type(40) == type('40')

# Type of value from a comparison.
type(type(40) == type('40'))

# Casting a bool to an int.
int(type(40) == type('40'))

# Example of a floating number.
2.6

# Find the type of a floating number.
type(2.6)

# Adding an integer to a float results in a value of type float.
40 + 2.6

type(40 + 2.6)

# Divide two integers.
11 / 40

# Type of resulting value from dividing two integers.
type(11 / 40)

# Example of casting integer prior to performing math operations required for Python 2.7 and earlier.
# 11/float(40)

# Complex numbers are represented by a real + imaginary part. The imaginary part is indicated by adding a suffix j.
1.5 + 2j

type((1.5 + 2j))

a_boolean_value = (type(40) == type('40'))

print(a_boolean_value)

print(11/float(40))

print('{} is {} years old and spent {} in school, that is {}% of his life.'.format('Bob',40, 11,100*float(11)/40))

print('{0} is {1} years old and spent {2} in school, that is {3}% of his life.'.format('Bob',40, 11,100*float(11)/40))

print('{0} is {1} years old and spent {2} in school, that is {3:.0f}% of his life.'.format('Bob',40,11,100*float(11)/40))

firstname = 'Davide'
lastname = 'La Torre'
print('{} {}' .format(firstname,lastname))

# Lists.
lst = ['this', 'is', 'a', 'list']
print(lst)

# Print the number of elements in the list.
print('The list contains {} elements.'.format(len(lst)))

# Print the first element in the list.
print(lst[0])

# Print the third element in the list.
print(lst[2])

# Print the last element in the list.
print(lst[-1])

# Appending a list.
print(lst)
lst.append('with')
lst.append('appended')
lst.append('elements')
print(lst)

# Print the number of elements in the list.
print('The updated list contains {} elements.'.format(len(lst)))

# Changing a list.
# Note: Remember that Python starts with index 0.
lst[0] = 'THIS'
lst[3] = lst[3].upper()
print(lst)

# Define a list of numbers.
numlist = [0, 10, 2, 7, 8, 5, 6, 3, 4, 1, 9]
print(numlist)

# Sort and filter list.
sorted_and_filtered_numlist = sorted(i for i in numlist if i >= 5)
print(sorted_and_filtered_numlist)

# Remove the last element from the list.
list.pop(sorted_and_filtered_numlist)
print(sorted_and_filtered_numlist)

tup = ('this', 'is', 'a', 'bigger', 'tuple')
print(tup)

tup[3]

# Tuples cannot be changed and will fail with an error if you try to change an element.
tup[3] = 'new'

# Generate a list of 10 values.
myrange = list(range(10))
myrange

# Generate a list with start value equal to one, stop value equal to ten that increments by three.
myrange2 = list(range(1, 10, 3))
myrange2

# Generate a negative list.
myrange3 = list(range(0, -10, -1))
myrange3

# You can specify the list manually or use a variable containing the list as input.
# The syntax for manual input is: `for item in [1, 2, 3]:`

for item in myrange:
    print(item)
    print(item * 'X')
print('End of loop (not included in the loop)')

# Flip a coin.

import random
coin_result = random.randrange(0,2)

print(coin_result)

if coin_result == 0:
    print('Heads')
else:
    print('Tails')

# Function 'myfirstfunction' with argument 'x'.
def myfirstfunction(x):
    y = x * 6
    return y


# Call your function.   
z = myfirstfunction(6)
print(z)

import random
random.random()

def coinflip(total_number_of_flips):
    '''
    function 'coinflip' with argument 'total_number_of_flips' for the number of repetitions 
    that returns the number of 'tail' occurrences
    '''
    # Set all starting variables to 0.
    heads = 0
    tails = 0
    current_number_of_flips = 0
    # Start a loop that executes statements while the conditional specified results in 'True'.
    while current_number_of_flips < total_number_of_flips:
        # Generate a random number smaller than 2.
        current_flip = random.randrange(0,2)
        # Increment heads by 1 if the generated number is 0.
        if current_flip == 0:
            heads = heads + 1
        # Increment tails by 1 if the generated number is larger than 0.
        if current_flip == 1:
            tails = tails + 1
        # Increment the flip variable by 1.
        current_number_of_flips += 1
    return [heads, tails]

import datetime 
now = datetime.datetime.now() 
random.seed(now)
for k in range(0,10):
    print(random.randrange(0,2))

# Set the number of repetitions.
num_flips = 100

# Call the function and set the output to the variable 'tails'.
coinflip_results = coinflip(num_flips)

# Output the values returned.
print(coinflip_results)

num_flips = 10000
coinflip_results = coinflip(num_flips)
print('Heads returned: {:.0f}%'.format(100*float(coinflip_results[0])/sum(coinflip_results)))

import random
Side_1 = 0
Side_2 = 0
Side_3 = 0
Side_4 = 0
Side_5 = 0
Side_6 = 0
current_number_of_flips = 0
total_number_of_flips = 60000
while current_number_of_flips < total_number_of_flips:
    current_flip = random.randrange(1,7)
    if current_flip == 1:
        Side_1 = Side_1 + 1
    if current_flip == 2:
        Side_2 = Side_2 + 1
    if current_flip == 3:
        Side_3 = Side_3 + 1
    if current_flip == 4:
        Side_4 = Side_4 + 1
    if current_flip == 5:
        Side_5 = Side_5 + 1
    if current_flip == 2:
        Side_6 = Side_6 + 1
    current_number_of_flips += 1
print(Side_1,Side_2,Side_3,Side_4,Side_5,Side_6) 

# Import matplotlib library and set notebook plotting options.
import matplotlib.pyplot as plt
import numpy as np
# Instruct the notebook to plot inline. 
%matplotlib inline     
plt.rcParams['figure.figsize'] = (15, 9) 
plt.rcParams['axes.titlesize'] = 'large'

# Generate data.
t = np.arange(0.0, 2.0, 0.01)
s = np.sin(2*np.pi*t)

# Create plot.
plt.plot(t, s)

# Set plot options.
plt.xlabel('time (s)')
plt.ylabel('voltage (mV)')
plt.title('About as simple as it gets, folks')
plt.grid(True)

# Saving as file can be achieved by uncommenting the line below.
# plt.savefig("test.png")

# Display the plot in the notebook.
# The '%matplotlib inline' option set earlier ensure that the plot is displayed inline.
plt.show()

"""
Simple demo with multiple subplots.
"""
import numpy as np
import matplotlib.pyplot as plt


x1 = np.linspace(0.0, 10.0)
x2 = np.linspace(0.0, 2.0)

y1 = np.cos(2 * np.pi * x1) * np.exp(-x1)
y2 = np.cos(2 * np.pi * x2)

plt.subplot(2, 1, 1)
plt.plot(x1, y1, 'yo-')
plt.title('A tale of 2 subplots')
plt.ylabel('Damped oscillation')

plt.subplot(2, 1, 2)
plt.plot(x2, y2, 'r.-')
plt.xlabel('time (s)')
plt.ylabel('Undamped')

plt.show()

"""
Demo of the histogram (hist) function with a few features.

In addition to the basic histogram, this demo shows a few optional features:

    * Setting the number of data bins
    * The ``normed`` flag, which normalizes bin heights so that the integral of
      the histogram is 1. The resulting histogram is a probability density.
    * Setting the face color of the bars
    * Setting the opacity (alpha value).

"""
import numpy as np
import matplotlib.mlab as mlab
import matplotlib.pyplot as plt


# Example data.
mu = 100  # Mean of distribution.
sigma = 15  # Standard deviation of distribution.
x = mu + sigma * np.random.randn(10000)

num_bins = 50
# The histogram of the data.
n, bins, patches = plt.hist(x, num_bins, normed=1, facecolor='green', alpha=0.5)
# Add a 'best fit' line.
y = mlab.normpdf(bins, mu, sigma)
plt.plot(bins, y, 'r--')
plt.xlabel('Smarts')
plt.ylabel('Probability')
plt.title(r'Histogram of IQ: $\mu=100$, $\sigma=15$')

# Tweak spacing to prevent clipping of ylabel.
plt.subplots_adjust(left=0.15)
plt.show()

threshold = 0.5
ntrials = 10 # Flips per trial.
size = 1000 # Repetitions.
M = [0 for x in range(0,size)] 
for i in range(0,size):
    M[i] = sum([random.random()< threshold for x in range(0,ntrials)])

# Cross tabulation for frequency of occurence of values.
M_xtab = [0 for i in range(0, ntrials)]
for i in range(0,ntrials):

print(M_xtab)

# The histogram of the data.
plt.hist(M, range=(0,ntrials),bins=ntrials)
plt.xlabel('Number  of heads')
plt.ylabel('Frequency')
plt.title('Coin flip distribution')

threshold = 0.2
ntrials = 20 # Flips per trial.
size = 1000 # Repetitions.
M = [0 for x in range(0,size)] 
for i in range(0,size):
    M[i] = sum([random.random()< threshold for x in range(0,ntrials)])
M_xtab = [0 for i in range(0, ntrials)]
for i in range(0,ntrials):
    M_xtab[i] = sum([x == i for x in M])
plt.hist(M, range=(0,ntrials),bins=ntrials)
plt.xlabel('Number  of heads')
plt.ylabel('Frequency')
plt.title('Coin flip distribution')