<?xml version='1.0' encoding='utf-8'?>
<scheme version="2.0" title="" description="">
	<nodes>
		<node id="0" name="Data Table" qualified_name="Orange.widgets.obsolete.owtable.OWDataTable" project_name="" version="" title="Data Table" position="(264.6363636363636, 328.7954545454545)" />
		<node id="1" name="Linear Regression" qualified_name="Orange.widgets.model.owlinearregression.OWLinearRegression" project_name="Orange3" version="" title="Linear Regression" position="(734.0, 330.0)" />
		<node id="2" name="Data Table" qualified_name="Orange.widgets.obsolete.owtable.OWDataTable" project_name="" version="" title="Data Table (1)" position="(936.0, 146.0)" />
		<node id="3" name="Test and Score" qualified_name="Orange.widgets.evaluate.owtestandscore.OWTestAndScore" project_name="Orange3" version="" title="Test and Score" position="(753.0, 580.0)" />
		<node id="4" name="Data Table" qualified_name="Orange.widgets.obsolete.owtable.OWDataTable" project_name="" version="" title="Data Table (2)" position="(935.0, 576.0)" />
		<node id="5" name="CSV File Import" qualified_name="Orange.widgets.data.owcsvimport.OWCSVFileImport" project_name="Orange3" version="" title="CSV File Import" position="(115.45454545454545, 336.0)" />
		<node id="6" name="Select Columns" qualified_name="Orange.widgets.data.owselectcolumns.OWSelectAttributes" project_name="Orange3" version="" title="Select Columns" position="(488.0, 323.0)" />
		<node id="7" name="Scatter Plot" qualified_name="Orange.widgets.visualize.owscatterplot.OWScatterPlot" project_name="Orange3" version="" title="Scatter Plot" position="(555.0, 83.0)" />
		<node id="8" name="Python Script" qualified_name="Orange.widgets.data.owpythonscript.OWPythonScript" project_name="Orange3" version="" title="Python Script" position="(705.0, 83.0)" />
		<node id="9" name="Logistic Regression" qualified_name="Orange.widgets.model.owlogisticregression.OWLogisticRegression" project_name="Orange3" version="" title="Logistic Regression" position="(424.5454545454545, 639.090909090909)" />
		<node id="10" name="Select Columns" qualified_name="Orange.widgets.data.owselectcolumns.OWSelectAttributes" project_name="Orange3" version="" title="Select Columns (1)" position="(207.72727272727263, 453.75)" />
		<node id="11" name="Test and Score" qualified_name="Orange.widgets.evaluate.owtestandscore.OWTestAndScore" project_name="Orange3" version="" title="Test and Score (1)" position="(753.6363636363637, 763.6363636363637)" />
		<node id="12" name="Data Table" qualified_name="Orange.widgets.obsolete.owtable.OWDataTable" project_name="" version="" title="Data Table (3)" position="(973.6363636363635, 768.1818181818182)" />
		<node id="13" name="Distributions" qualified_name="Orange.widgets.visualize.owdistributions.OWDistributions" project_name="Orange3" version="" title="Distributions" position="(341.8181818181818, 79.0909090909091)" />
		<node id="14" name="Correlations" qualified_name="Orange.widgets.data.owcorrelations.OWCorrelations" project_name="Orange3" version="" title="Correlations" position="(162.63636363636363, 96.0)" />
		<node id="15" name="Neural Network" qualified_name="Orange.widgets.model.owneuralnetwork.OWNNLearner" project_name="Orange3" version="" title="Neural Network" position="(293.63636363636357, 710.9090909090908)" />
		<node id="16" name="SVM" qualified_name="Orange.widgets.model.owsvm.OWSVM" project_name="Orange3" version="" title="SVM" position="(123.63636363636371, 797.2727272727269)" />
		<node id="17" name="Random Forest" qualified_name="Orange.widgets.model.owrandomforest.OWRandomForest" project_name="Orange3" version="" title="Random Forest" position="(303.0, 847.0)" />
	</nodes>
	<links>
		<link id="0" source_node_id="1" sink_node_id="2" source_channel="Coefficients" sink_channel="Data" enabled="true" source_channel_id="coefficients" sink_channel_id="data" />
		<link id="1" source_node_id="1" sink_node_id="3" source_channel="Learner" sink_channel="Learner" enabled="true" source_channel_id="learner" sink_channel_id="learner" />
		<link id="2" source_node_id="3" sink_node_id="4" source_channel="Predictions" sink_channel="Data" enabled="true" source_channel_id="predictions" sink_channel_id="data" />
		<link id="3" source_node_id="0" sink_node_id="6" source_channel="Selected Data" sink_channel="Data" enabled="true" source_channel_id="selected_data" sink_channel_id="data" />
		<link id="4" source_node_id="6" sink_node_id="1" source_channel="Data" sink_channel="Data" enabled="true" source_channel_id="data" sink_channel_id="data" />
		<link id="5" source_node_id="6" sink_node_id="3" source_channel="Data" sink_channel="Data" enabled="true" source_channel_id="data" sink_channel_id="train_data" />
		<link id="6" source_node_id="5" sink_node_id="0" source_channel="Data" sink_channel="Data" enabled="true" source_channel_id="data" sink_channel_id="data" />
		<link id="7" source_node_id="6" sink_node_id="7" source_channel="Data" sink_channel="Data" enabled="true" source_channel_id="data" sink_channel_id="data" />
		<link id="8" source_node_id="0" sink_node_id="10" source_channel="Selected Data" sink_channel="Data" enabled="true" source_channel_id="selected_data" sink_channel_id="data" />
		<link id="9" source_node_id="10" sink_node_id="9" source_channel="Data" sink_channel="Data" enabled="true" source_channel_id="data" sink_channel_id="data" />
		<link id="10" source_node_id="9" sink_node_id="11" source_channel="Learner" sink_channel="Learner" enabled="true" source_channel_id="learner" sink_channel_id="learner" />
		<link id="11" source_node_id="10" sink_node_id="11" source_channel="Data" sink_channel="Data" enabled="true" source_channel_id="data" sink_channel_id="train_data" />
		<link id="12" source_node_id="11" sink_node_id="12" source_channel="Predictions" sink_channel="Data" enabled="true" source_channel_id="predictions" sink_channel_id="data" />
		<link id="13" source_node_id="0" sink_node_id="13" source_channel="Selected Data" sink_channel="Data" enabled="true" source_channel_id="selected_data" sink_channel_id="data" />
		<link id="14" source_node_id="0" sink_node_id="14" source_channel="Selected Data" sink_channel="Data" enabled="true" source_channel_id="selected_data" sink_channel_id="data" />
		<link id="15" source_node_id="10" sink_node_id="15" source_channel="Data" sink_channel="Data" enabled="true" source_channel_id="data" sink_channel_id="data" />
		<link id="16" source_node_id="15" sink_node_id="11" source_channel="Learner" sink_channel="Learner" enabled="true" source_channel_id="learner" sink_channel_id="learner" />
		<link id="17" source_node_id="10" sink_node_id="16" source_channel="Data" sink_channel="Data" enabled="true" source_channel_id="data" sink_channel_id="data" />
		<link id="18" source_node_id="16" sink_node_id="11" source_channel="Learner" sink_channel="Learner" enabled="true" source_channel_id="learner" sink_channel_id="learner" />
		<link id="19" source_node_id="10" sink_node_id="17" source_channel="Data" sink_channel="Data" enabled="true" source_channel_id="data" sink_channel_id="data" />
		<link id="20" source_node_id="17" sink_node_id="11" source_channel="Learner" sink_channel="Learner" enabled="true" source_channel_id="learner" sink_channel_id="learner" />
	</links>
	<annotations />
	<thumbnail />
	<node_properties>
		<properties node_id="0" format="literal">{'auto_commit': True, 'color_by_class': True, 'controlAreaVisible': True, 'savedWidgetGeometry': b'\x01\xd9\xd0\xcb\x00\x03\x00\x00\x00\x00\x01\xea\x00\x00\x00\xe0\x00\x00\x08\xa6\x00\x00\x04\x8e\x00\x00\x01\xeb\x00\x00\x00\xfe\x00\x00\x08\xa5\x00\x00\x04\x8d\x00\x00\x00\x00\x00\x00\x00\x00\n\x00\x00\x00\x01\xeb\x00\x00\x00\xfe\x00\x00\x08\xa5\x00\x00\x04\x8d', 'select_rows': True, 'selected_cols': [], 'selected_rows': [], 'show_attribute_labels': True, 'show_distributions': False, '__version__': 2}</properties>
		<properties node_id="1" format="literal">{'alpha_index': 0, 'auto_apply': True, 'autosend': True, 'controlAreaVisible': True, 'fit_intercept': True, 'l2_ratio': 0.5, 'learner_name': '', 'reg_type': 2, 'ridge': False, 'savedWidgetGeometry': b"\x01\xd9\xd0\xcb\x00\x03\x00\x00\x00\x00\x01\xdb\x00\x00\x00\xd8\x00\x00\x03'\x00\x00\x02?\x00\x00\x01\xdc\x00\x00\x00\xf6\x00\x00\x03&amp;\x00\x00\x02&gt;\x00\x00\x00\x00\x00\x00\x00\x00\x05\x00\x00\x00\x01\xdc\x00\x00\x00\xf6\x00\x00\x03&amp;\x00\x00\x02&gt;", '__version__': 1}</properties>
		<properties node_id="2" format="literal">{'auto_commit': True, 'color_by_class': True, 'controlAreaVisible': True, 'savedWidgetGeometry': b'\x01\xd9\xd0\xcb\x00\x03\x00\x00\x00\x00\x03\xc0\x00\x00\x01\xc8\x00\x00\x06A\x00\x00\x03\xc6\x00\x00\x03\xc1\x00\x00\x01\xe6\x00\x00\x06@\x00\x00\x03\xc5\x00\x00\x00\x00\x00\x00\x00\x00\n\x00\x00\x00\x03\xc1\x00\x00\x01\xe6\x00\x00\x06@\x00\x00\x03\xc5', 'select_rows': True, 'selected_cols': [], 'selected_rows': [], 'show_attribute_labels': True, 'show_distributions': False, '__version__': 2}</properties>
		<properties node_id="3" format="pickle">gASVuwYAAAAAAAB9lCiMFGNvbXBhcmlzb25fY3JpdGVyaW9ulEsAjBJjb250cm9sQXJlYVZpc2li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</properties>
		<properties node_id="4" format="literal">{'auto_commit': True, 'color_by_class': True, 'controlAreaVisible': True, 'savedWidgetGeometry': b'\x01\xd9\xd0\xcb\x00\x03\x00\x00\x00\x00\x02\x02\x00\x00\x00\xff\x00\x00\x07\xe0\x00\x00\x04C\x00\x00\x02\x03\x00\x00\x01\x1d\x00\x00\x07\xdf\x00\x00\x04B\x00\x00\x00\x00\x00\x00\x00\x00\n\x00\x00\x00\x02\x03\x00\x00\x01\x1d\x00\x00\x07\xdf\x00\x00\x04B', 'select_rows': True, 'selected_cols': [], 'selected_rows': [], 'show_attribute_labels': True, 'show_distributions': False, '__version__': 2}</properties>
		<properties node_id="5" format="literal">{'_session_items': [], '_session_items_v2': [({'type': 'AbsPath', 'path': 'F:/BaiduSyncdisk/西交&amp;Skema&amp;MEI/人工智能应用/附件4-Datasets/Activity7_DuffyInudstries_RawData.csv'}, {'encoding': 'utf-8', 'delimiter': ',', 'quotechar': '"', 'doublequote': True, 'skipinitialspace': True, 'quoting': 0, 'columntypes': [{'start': 0, 'stop': 10, 'value': 'Auto'}], 'rowspec': [{'start': 0, 'stop': 1, 'value': 'Header'}], 'decimal_separator': '.', 'group_separator': ''}), ({'type': 'AbsPath', 'path': 'C:/Users/<USER>/Desktop/DuffyInudstries_RawData.csv'}, {'encoding': 'utf-8', 'delimiter': ',', 'quotechar': '"', 'doublequote': True, 'skipinitialspace': True, 'quoting': 0, 'columntypes': [{'start': 0, 'stop': 9, 'value': 'Auto'}], 'rowspec': [{'start': 0, 'stop': 1, 'value': 'Header'}], 'decimal_separator': '.', 'group_separator': ''})], 'compatibility_mode': False, 'controlAreaVisible': True, 'dialog_state': {'directory': 'F:/BaiduSyncdisk/西交&amp;Skema&amp;MEI/人工智能应用/附件4-Datasets', 'filter': 'Text - comma separated (*.csv, *)'}, 'savedWidgetGeometry': b'\x01\xd9\xd0\xcb\x00\x03\x00\x00\x00\x00\x04l\x00\x00\x02\x1c\x00\x00\x05\x95\x00\x00\x03r\x00\x00\x04m\x00\x00\x02:\x00\x00\x05\x94\x00\x00\x03q\x00\x00\x00\x00\x00\x00\x00\x00\n\x00\x00\x00\x04m\x00\x00\x02:\x00\x00\x05\x94\x00\x00\x03q', '__version__': 3}</properties>
		<properties node_id="6" format="pickle">gASVWQMAAAAAAAB9lCiMC2F1dG9fY29tbWl0lIiMEmNvbnRyb2xBcmVhVmlzaWJsZZSIjBNpZ25v
cmVfbmV3X2ZlYXR1cmVzlImME3NhdmVkV2lkZ2V0R2VvbWV0cnmUQ0IB2dDLAAMAAAAAA8AAAAHI
AAAGQQAAA8YAAAPBAAAB5gAABkAAAAPFAAAAAAAAAAAKAAAAA8EAAAHmAAAGQAAAA8WUjBJ1c2Vf
aW5wdXRfZmVhdHVyZXOUiYwLX192ZXJzaW9uX1+USwGMEGNvbnRleHRfc2V0dGluZ3OUXZQojBVv
cmFuZ2V3aWRnZXQuc2V0dGluZ3OUjAdDb250ZXh0lJOUKYGUfZQojAZ2YWx1ZXOUfZQojBFkb21h
aW5fcm9sZV9oaW50c5R9lCiMAklElEsChpSMCWF2YWlsYWJsZZRLAIaUjAZMZFRpbWWUSwKGlGgV
SwGGlIwFRXF1cHSUSwGGlGgVSwKGlIwHVHJsTG5nIJRLAoaUaBVLA4aUjANXZ3SUSwKGlGgVSwSG
lIwERGlzdJRLAoaUjAlhdHRyaWJ1dGWUSwCGlIwDQ1BMlEsChpSMBWNsYXNzlEsAhpR1Sv7///+G
lGgHSwF1jAphdHRyaWJ1dGVzlH2UKIwCSUSUSwKMA0NQTJRLAowERGlzdJRLAowGTGRUaW1llEsC
jAdUcmxMbmcglEsCjANXZ3SUSwKMBUVxdXB0lEsBdYwFbWV0YXOUfZR1YmgMKYGUfZQoaA99lCiM
EWRvbWFpbl9yb2xlX2hpbnRzlH2UKIwFRXF1cHSUSwGGlIwJYXZhaWxhYmxllEsAhpSMAklElEsC
hpRoPksBhpSMClVubmFtZWQ6IDGUSwKGlGg+SwKGlIwGRGlzdC4xlEsChpRoPksDhpSMBERpc3SU
SwKGlGg+SwSGlIwHVHJsTG5nIJRLAoaUaD5LBYaUjAZMZFRpbWWUSwKGlGg+SwaGlIwGRXFfbnVt
lEsBhpRoPksHhpSMA1dndJRLAoaUjAlhdHRyaWJ1dGWUSwCGlIwDQ1BMlEsChpSMBWNsYXNzlEsA
hpR1Sv7///+GlGgHSwF1aCx9lChoQEsCaENLAmhJSwJoWUsCaEZLAmhPSwJoTEsCaFVLAmg8SwFo
UksBdWg1fZR1YmV1Lg==
</properties>
		<properties node_id="7" format="pickle">gASVVgQAAAAAAAB9lCiMDWF0dHJfeF9pc19hYnOUiYwNYXR0cl95X2lzX2Fic5SJjAthdXRvX2Nv
bW1pdJSIjAthdXRvX3NhbXBsZZSIjBJjb250cm9sQXJlYVZpc2libGWUiIwTc2F2ZWRXaWRnZXRH
ZW9tZXRyeZRDQgHZ0MsAAwAAAAABiQAAAHsAAAX2AAADZQAAAYoAAAChAAAF9QAAA2QAAAAAAAAA
AAeAAAABigAAAKEAAAX1AAADZJSMCXNlbGVjdGlvbpROjBF0b29sdGlwX3Nob3dzX2FsbJSIjA92
aXN1YWxfc2V0dGluZ3OUfZSMBWdyYXBolH2UKIwLYWxwaGFfdmFsdWWUS4CMDWNsYXNzX2RlbnNp
dHmUiYwRaml0dGVyX2NvbnRpbnVvdXOUiYwLaml0dGVyX3NpemWUSwqME2xhYmVsX29ubHlfc2Vs
ZWN0ZWSUiYwWb3J0aG9ub3JtYWxfcmVncmVzc2lvbpSJjAtwb2ludF93aWR0aJRLCowMc2hvd19l
bGxpcHNllImMCXNob3dfZ3JpZJSJjAtzaG93X2xlZ2VuZJSIjA1zaG93X3JlZ19saW5llIl1jAtf
X3ZlcnNpb25fX5RLBYwQY29udGV4dF9zZXR0aW5nc5RdlCiMFW9yYW5nZXdpZGdldC5zZXR0aW5n
c5SMB0NvbnRleHSUk5QpgZR9lCiMBnZhbHVlc5R9lCiMCmF0dHJfY29sb3KUjANDUEyUS2aGlIwK
YXR0cl9sYWJlbJROSv7///+GlIwKYXR0cl9zaGFwZZROSv7///+GlIwJYXR0cl9zaXpllE5K/v//
/4aUjAZhdHRyX3iUjAREaXN0lEtmhpSMDGF0dHJfeF9sb3dlcpROSv7///+GlIwMYXR0cl94X3Vw
cGVylE5K/v///4aUjAZhdHRyX3mUaCRLZoaUjAxhdHRyX3lfbG93ZXKUTkr+////hpSMDGF0dHJf
eV91cHBlcpROSv7///+GlGgMfZRoGUsFdYwKYXR0cmlidXRlc5R9lCiMBERpc3SUSwKMA0NQTJRL
AnWMBW1ldGFzlH2UdWJoHimBlH2UKGghfZQojAphdHRyX2NvbG9ylIwDQ1BMlEtmhpSMCmF0dHJf
bGFiZWyUTkr+////hpSMCmF0dHJfc2hhcGWUTkr+////hpSMCWF0dHJfc2l6ZZROSv7///+GlIwG
YXR0cl94lIwDV2d0lEtmhpSMBmF0dHJfeZRoREtmhpSMBWdyYXBolH2UaBlLBXVoOn2UKGhNSwJo
REsCdWg+fZR1YmgeKYGUfZQoaCF9lChoQ2hES2aGlGhGTkr+////hpRoSE5K/v///4aUaEpOSv7/
//+GlGhMaE1LZoaUaE9oREtmhpRoUX2UaBlLBXVoOn2UKGhNSwKMBERpc3SUSwKMB1RybExuZyCU
SwKMBkxkVGltZZRLAowGRXFfbnVtlEsBaERLAnVoPn2UdWJldS4=
</properties>
		<properties node_id="8" format="literal">{'controlAreaVisible': True, 'currentScriptIndex': 0, 'savedWidgetGeometry': None, 'scriptLibrary': [{'name': 'Table from numpy', 'script': 'import numpy as np\nfrom Orange.data import Table, Domain, ContinuousVariable, DiscreteVariable\n\ndomain = Domain([ContinuousVariable("age"),\n                 ContinuousVariable("height"),\n                 DiscreteVariable("gender", values=("M", "F"))])\narr = np.array([\n  [25, 186, 0],\n  [30, 164, 1]])\nout_data = Table.from_numpy(domain, arr)\n', 'filename': None}], 'scriptText': 'import numpy as np\nfrom Orange.data import Table, Domain, ContinuousVariable, DiscreteVariable\n\ndomain = Domain([ContinuousVariable("age"),\n                 ContinuousVariable("height"),\n                 DiscreteVariable("gender", values=("M", "F"))])\narr = np.array([\n  [25, 186, 0],\n  [30, 164, 1]])\nout_data = Table.from_numpy(domain, arr)\n', 'splitterState': b'\x00\x00\x00\xff\x00\x00\x00\x01\x00\x00\x00\x02\x00\x00\x00\xa2\x00\x00\x00q\x01\xff\xff\xff\xff\x01\x00\x00\x00\x02\x00', 'vimModeEnabled': False, '__version__': 2}</properties>
		<properties node_id="9" format="literal">{'C_index': 75, 'auto_apply': False, 'class_weight': False, 'controlAreaVisible': True, 'learner_name': '', 'penalty_type': 0, 'savedWidgetGeometry': b'\x01\xd9\xd0\xcb\x00\x03\x00\x00\x00\x00\x03J\x00\x00\x01y\x00\x00\x045\x00\x00\x02\x8d\x00\x00\x03J\x00\x00\x01y\x00\x00\x045\x00\x00\x02\x8d\x00\x00\x00\x00\x00\x00\x00\x00\x07\x80\x00\x00\x03J\x00\x00\x01y\x00\x00\x045\x00\x00\x02\x8d', '__version__': 2}</properties>
		<properties node_id="10" format="pickle">gASVWQMAAAAAAAB9lCiMC2F1dG9fY29tbWl0lIiMEmNvbnRyb2xBcmVhVmlzaWJsZZSIjBNpZ25v
cmVfbmV3X2ZlYXR1cmVzlImME3NhdmVkV2lkZ2V0R2VvbWV0cnmUQ0IB2dDLAAMAAAAAAUAAAACM
AAADwQAAAooAAAFBAAAAqgAAA8AAAAKJAAAAAAAAAAAFAAAAAUEAAACqAAADwAAAAomUjBJ1c2Vf
aW5wdXRfZmVhdHVyZXOUiYwLX192ZXJzaW9uX1+USwGMEGNvbnRleHRfc2V0dGluZ3OUXZQojBVv
cmFuZ2V3aWRnZXQuc2V0dGluZ3OUjAdDb250ZXh0lJOUKYGUfZQojAZ2YWx1ZXOUfZQojBFkb21h
aW5fcm9sZV9oaW50c5R9lCiMAklElEsChpSMCWF2YWlsYWJsZZRLAIaUjANXZ3SUSwKGlGgVSwGG
lIwHVHJsTG5nIJRLAoaUaBVLAoaUjAREaXN0lEsChpSMCWF0dHJpYnV0ZZRLAIaUjAZMZFRpbWWU
SwKGlGgfSwGGlIwDQ1BMlEsChpRoH0sChpSMBUVxdXB0lEsBhpSMBWNsYXNzlEsAhpR1Sv7///+G
lGgHSwF1jAphdHRyaWJ1dGVzlH2UKIwCSUSUSwKMA0NQTJRLAowERGlzdJRLAowGTGRUaW1llEsC
jAdUcmxMbmcglEsCjANXZ3SUSwKMBUVxdXB0lEsBdYwFbWV0YXOUfZR1YmgMKYGUfZQoaA99lCiM
EWRvbWFpbl9yb2xlX2hpbnRzlH2UKIwFRXF1cHSUSwGGlIwJYXZhaWxhYmxllEsAhpSMAklElEsC
hpRoPksBhpSMClVubmFtZWQ6IDGUSwKGlGg+SwKGlIwGRGlzdC4xlEsChpRoPksDhpSMBERpc3SU
SwKGlGg+SwSGlIwHVHJsTG5nIJRLAoaUaD5LBYaUjAZMZFRpbWWUSwKGlGg+SwaGlIwGRXFfbnVt
lEsBhpRoPksHhpSMA1dndJRLAoaUjAlhdHRyaWJ1dGWUSwCGlIwDQ1BMlEsChpSMBWNsYXNzlEsA
hpR1Sv7///+GlGgHSwF1aCx9lChoQEsCaENLAmhJSwJoWUsCaEZLAmhPSwJoTEsCaFVLAmg8SwFo
UksBdWg1fZR1YmV1Lg==
</properties>
		<properties node_id="11" format="pickle">gASVIgYAAAAAAAB9lCiMFGNvbXBhcmlzb25fY3JpdGVyaW9ulEsAjBJjb250cm9sQXJlYVZpc2li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</properties>
		<properties node_id="12" format="literal">{'auto_commit': True, 'color_by_class': True, 'controlAreaVisible': True, 'savedWidgetGeometry': b'\x01\xd9\xd0\xcb\x00\x03\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1d\x00\x00\x07\x7f\x00\x00\x04\x05\x00\x00\x00\x00\x00\x00\x00\x1d\x00\x00\x07\x7f\x00\x00\x04\x05\x00\x00\x00\x00\x02\x00\x00\x00\x07\x80\x00\x00\x00\x00\x00\x00\x00\x1d\x00\x00\x07\x7f\x00\x00\x04\x05', 'select_rows': True, 'selected_cols': [], 'selected_rows': [], 'show_attribute_labels': True, 'show_distributions': False, '__version__': 2}</properties>
		<properties node_id="13" format="pickle">gASV5AIAAAAAAAB9lCiMCmF1dG9fYXBwbHmUiIwSY29udHJvbEFyZWFWaXNpYmxllIiMEGN1bXVs
YXRpdmVfZGlzdHKUiYwTZml0dGVkX2Rpc3RyaWJ1dGlvbpRLAYwJaGlkZV9iYXJzlImMDWtkZV9z
bW9vdGhpbmeUSwqME3NhdmVkV2lkZ2V0R2VvbWV0cnmUQ0IB2dDLAAMAAAAAAeYAAADZAAAFmQAA
Ay0AAAHmAAAA2QAABZkAAAMtAAAAAAAAAAAHgAAAAeYAAADZAAAFmQAAAy2UjApzaG93X3Byb2Jz
lImMDHNvcnRfYnlfZnJlcZSJjA9zdGFja2VkX2NvbHVtbnOUiYwLX192ZXJzaW9uX1+USwKMEGNv
bnRleHRfc2V0dGluZ3OUXZQojBVvcmFuZ2V3aWRnZXQuc2V0dGluZ3OUjAdDb250ZXh0lJOUKYGU
fZQojAZ2YWx1ZXOUfZQojARjdmFylE5K/v///4aUjA5udW1iZXJfb2ZfYmluc5RLAEr+////hpSM
DXNlbGVjdGVkX2JhcnOUj5RK/v///4aUjAN2YXKUjANDUEyUS2aGlGgMSwJ1jAphdHRyaWJ1dGVz
lH2UKIwCSUSUSwKMA0NQTJRLAowERGlzdJRLAowGTGRUaW1llEsCjAdUcmxMbmcglEsCjANXZ3SU
SwKMBUVxdXB0lEsBdYwFbWV0YXOUfZR1YmgRKYGUfZQoaBR9lCiMBGN2YXKUTkr+////hpSMDm51
bWJlcl9vZl9iaW5zlEsASv7///+GlIwNc2VsZWN0ZWRfYmFyc5SPlEr+////hpSMA3ZhcpSMA0NQ
TJRLZoaUaAxLAnVoIH2UKIwCSUSUSwKMClVubmFtZWQ6IDGUSwKMBERpc3SUSwJoNksCjAZEaXN0
LjGUSwKMBkxkVGltZZRLAowHVHJsTG5nIJRLAowDV2d0lEsCjAVFcXVwdJRLAYwGRXFfbnVtlEsB
dWgpfZR1YmV1Lg==
</properties>
		<properties node_id="14" format="pickle">gASVEAIAAAAAAAB9lCiMEmNvbnRyb2xBcmVhVmlzaWJsZZSIjBBjb3JyZWxhdGlvbl90eXBllEsA
jBNzYXZlZFdpZGdldEdlb21ldHJ5lENCAdnQywADAAAAAAPAAAAByAAABkEAAAPGAAADwQAAAeYA
AAZAAAADxQAAAAAAAAAACgAAAAPBAAAB5gAABkAAAAPFlIwLX192ZXJzaW9uX1+USwOMEGNvbnRl
eHRfc2V0dGluZ3OUXZQojBVvcmFuZ2V3aWRnZXQuc2V0dGluZ3OUjAdDb250ZXh0lJOUKYGUfZQo
jAZ2YWx1ZXOUfZQojAdmZWF0dXJllIwDQ1BMlEtmhpSMCXNlbGVjdGlvbpRdlChoEEtmhpSMBERp
c3SUS2aGlGVK/f///4aUaAVLA3WMCmF0dHJpYnV0ZXOUfZQojAJJRJRLAowDQ1BMlEsCjAREaXN0
lEsCjAZMZFRpbWWUSwKMB1RybExuZyCUSwKMA1dndJRLAnWMBW1ldGFzlH2UdWJoCimBlH2UKGgN
fZQojAdmZWF0dXJllIwDQ1BMlEtmhpSMCXNlbGVjdGlvbpRdlChoJktmhpSMBERpc3SUS2aGlGVK
/f///4aUaAVLA3VoGH2UKIwCSUSUSwJoK0sCaCZLAowGRGlzdC4xlEsCjAZMZFRpbWWUSwKMB1Ry
bExuZyCUSwKMA1dndJRLAnVoIH2UdWJldS4=
</properties>
		<properties node_id="15" format="literal">{'activation_index': 3, 'alpha_index': 1, 'auto_apply': True, 'controlAreaVisible': True, 'hidden_layers_input': '100,', 'learner_name': 'Neural Network', 'max_iterations': 200, 'replicable': True, 'savedWidgetGeometry': b'\x01\xd9\xd0\xcb\x00\x03\x00\x00\x00\x00\x03\x16\x00\x00\x01A\x00\x00\x04i\x00\x00\x02\xa0\x00\x00\x03\x17\x00\x00\x01g\x00\x00\x04h\x00\x00\x02\x9f\x00\x00\x00\x00\x00\x00\x00\x00\x07\x80\x00\x00\x03\x17\x00\x00\x01g\x00\x00\x04h\x00\x00\x02\x9f', 'solver_index': 2, '__version__': 2}</properties>
		<properties node_id="16" format="literal">{'C': 1.0, 'auto_apply': True, 'coef0': 1.0, 'controlAreaVisible': True, 'degree': 3, 'epsilon': 0.1, 'gamma': 0.0, 'kernel_type': 2, 'learner_name': '', 'limit_iter': True, 'max_iter': 100, 'nu': 0.5, 'nu_C': 1.0, 'savedWidgetGeometry': b'\x01\xd9\xd0\xcb\x00\x03\x00\x00\x00\x00\x03\x1e\x00\x00\x00\xe0\x00\x00\x04b\x00\x00\x03\x00\x00\x00\x03\x1f\x00\x00\x01\x06\x00\x00\x04a\x00\x00\x02\xff\x00\x00\x00\x00\x00\x00\x00\x00\x07\x80\x00\x00\x03\x1f\x00\x00\x01\x06\x00\x00\x04a\x00\x00\x02\xff', 'svm_type': 0, 'tol': 0.001, '__version__': 2}</properties>
		<properties node_id="17" format="literal">{'auto_apply': True, 'class_weight': False, 'controlAreaVisible': True, 'index_output': 0, 'learner_name': '', 'max_depth': 3, 'max_features': 5, 'min_samples_split': 5, 'n_estimators': 10, 'savedWidgetGeometry': None, 'use_max_depth': False, 'use_max_features': False, 'use_min_samples_split': True, 'use_random_state': False, '__version__': 1}</properties>
	</node_properties>
	<session_state>
		<window_groups />
	</session_state>
</scheme>
