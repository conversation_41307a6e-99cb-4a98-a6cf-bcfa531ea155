{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<div align=\"right\">Python 3.6 Jupyter Notebook</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to Python"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Your completion of the notebook exercises will be give you ability to do the following:\n", "\n", "> **Understand**: Do your pseudo-code and comments show evidence that you recall and understand technical concepts?\n", "\n", "> **Apply**: Are you able to execute code (using the supplied examples) that performs the required functionality on supplied or generated data sets?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Notebook objectives\n", "By the end of this notebook you will be expected to:\n", "> \n", "  - Identify **basic concepts of programming**;\n", "  - Understand **Python syntax**;\n", "  - Distinguish between the different **native data types** used in Python;\n", "  - Use **flow control structures** to implement **conditional and repetitive logic**;\n", "  - Understand **functions** as a block of organized, reusable code that is used to perform a single, related action, as being necessary for improving the modularity of an application, as well as allowing for code reuse within and across applications; and\n", "  - Understand a Python **package** as a file consisting of Python code that defines functions, classes, variables, and may also include runnable code. You should be able to **import** a Python package or specific functions from a package.\n", " \n", "####  List of exercises\n", ">   - Exercise 1: Printing strings.\n", "  - Exercise 2: Rolling dice.\n", "  - Exercise 3: Coin flip distribution."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Notebook introduction\n", "\n", "This section will serve as a summarized version of useful commands that are needed to get non-technical users started, and equip them with the basics required to complete this course.\n", "\n", "This course is not intended to be a Python training course, but rather to showcase how Python can be utilized in analysis projects. You will be provided with links that you can explore in your own time in order to further your expertise. \n", "\n", "You should execute the cells with sample code and write your own code in the indicated cells in this notebook. When complete, ensure that you save and checkpoint the notebook, download a copy to your local workstation, and submit a copy to the Online Campus.\n", "\n", "You can also visit the [Python Language Reference](https://docs.python.org/3/reference/index.html) for a more complete reference manual that describes the syntax and \"core semantics\" of the language.\n", "\n", "> **Note to new Python users**: \n", "\n", "> This notebook will introduce a significant amount of new content. You do not need to be able to master all of the components, but you are urged to work through the examples below, and to attempt following the logic. In this notebook, you will be introduced to Python syntax. The second notebook in this module will start to introduce various components of data analysis basics, and the third will guide you through a basic example from beginning to end. The focus will shift from programming to subject-related examples in subsequent modules."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-warning\">\n", "<b>Note</b>:<br>\n", "It is strongly recommended that you save and checkpoint after applying significant changes or completing exercises. This allows you to return the notebook to a previous state should you wish to do so. On the Jupyter menu, select \"File\", then \"Save and Checkpoint\" from the dropdown menu that appears.\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. Introduction to Python programming"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Python is generally [defined](https://docs.python.org/3/faq/general.html#what-is-python) as a *high-level, general-purpose, interpreted, dynamic computer programming language*. \n", "\n", "Let’s try to break this statement down, for those new to computer programming. High-level means that we can express instructions that computers are able to execute in a language that is closer to a human language, and not the machine language that a computer requires to run the instructions. The interpreter is a program that converts the high-level programming instructions into low-level programming instructions that a computer understands. Dynamic means that they do not enforce or check type-safety at “compile-time”, in favor of deferring such checks until “run-time”. This has many advantages, including lower development costs, rapid prototyping, and flexibility required by specific domains, such as data processing and analysis. This makes Python a perfect language for this course, as it is easy to understand (the expressions used are very similar to day-to-day conversations), it has a simple syntax, and provides objects that are appropriate for data processing and analysis.\n", "\n", "Because it’s a programming language, there are some basic computer science concepts that are important to understand before you start coding.\n", "\n", "## 1.1 Basic concepts\n", "\n", "### 1.1.1 Variables\n", "Variables are the backbone of any program and, therefore, the backbone of any programming language. A variable is used to store information to be referenced and manipulated in a set of instructions commonly referred to as a computer program. Variables also provide a way of labeling data with a descriptive name, in order for programs to be understood more clearly by the reader and ourselves (recall high-level from above). It is helpful to think of variables as containers that hold information. Their sole purpose is to label and store data in the computer's memory. This data can then be used throughout a program.\n", "\n", "Variables can store various pieces of information such as the name of a person, that person's age or weight, or their date of birth. Each of these pieces of information contain different types of information. The data type of the variable containing the name of a person would be a string, while the age is stored as an integer, and the date of birth as a datetime object. Fortunately, in Python, unlike in Java or C, you do not need to tell the computer what type a variable is before you assign an object to it. This means that you can begin your program by referring to your name, say, as “<PERSON>”.\n", "\n", "    >>> X = 'Mary'\n", "    \n", "In the middle of your program, you might change your mind, and reassign your “X” variable to your age.\n", "\n", "    >>> X = 21\n", "\n", "This provides you with flexibility that you can exploit to quickly test ideas without worrying that your code will break when executed. As with anything, \"with great flexibility, comes great responsibility!\" It is best practice to use names that are intuitive for the data that the variable stores. The only exception is using names which are reserved by the programming language, called **keywords**. Keywords define the language's rules and structure, and they cannot be used as variable names. Inside Jupyter, a Python reserved name is highlighted differently to other names, as will shortly become clear.\n", "\n", "A variable’s type also defines what you can do, and the behaviors you can expect, when you perform certain operations on it. This is illustrated later in this notebook.\n", "\n", "### 1.1.2 Control structures\n", "When we put together a set of instructions for the computer to execute, the computer reads the instructions sequentially. This is known as the \"flow of the code\". However, it is often the case that an instruction in the code requires a decision to be made. For example, say you have written a program to accept interns into your organization. To comply with the labor regulations governing your organization, you ask each candidate to enter their age amongst other details. Before accepting them as an intern, your program checks if the person's age is within the prescribed limits. If not, the application of a potential intern is rejected. Such decision points that affect the flow of the code occur frequently in computer programming and are known as **flow control structures**. [Specifically](https://ocw.mit.edu/courses/electrical-engineering-and-computer-science/6-096-introduction-to-c-january-iap-2011/lecture-notes/MIT6_096IAP11_lec02.pdf):\n", "\n", "> A control structure is a block of programming that analyzes variables and chooses a direction in which to go based on given parameters. The term flow control details the direction the program takes (which way program control “flows”). Hence it is the basic decision-making process in computing; flow control determines how a computer will respond when given certain conditions and parameters.\n", "\n", "Control structures make the programs function properly and, without control structures, your program’s code would only flow linearly from top to bottom. Changing what action a program takes, based on a variable's value, is what makes programming useful. Below, you will be introduced to the different control structures available in Python.\n", "\n", "### 1.1.3 Data structures\n", "A data structure is a particular way of storing and organizing data in a computer so that it can be used efficiently. Previously, you were introduced to the concept of variables using a person's name, age, and date of birth as examples. Imagine doing that for everyone who is enrolled in the MIT Big Data and Social Analytics certificate course. That would be a lot of variables that you would struggle to keep track of. A data structure is a way to get around having to create millions of variables. Python implements many data structures that you will be using, such as lists, dicts, tuples, and sequences. Moreover, other data structures can be built on top of these for efficiently working with data. In Python, the NumPy, and Pandas, modules provide data structures that are intuitive and expressive enough for many of our needs in data analysis.\n", "\n", "### 1.1.4 Syntax\n", "Each programming language is designed and implemented differently, and using it requires adherence to its syntax, which is the set of rules that define the combinations of symbols that are considered to be correctly-structured programs in that language. When these rules are followed, the programming language can understand your instructions and, therefore, you are able to create software that can be run. If you do not adhere to the rules of programming languages’ syntax, you will unfortunately run into errors and your programs will not be executed. As it turns out, the syntax of any programming language tends to be the biggest obstacle many people face when being introduced to programming for the first time, or when learning a new programming language. (Note that syntax errors are only one type of error among many that are possible.) Fortunately, Python will correctly identify syntax errors as it encounters them. This will be useful when trying to find errors in your code. Some key things to remember when using Python are highlighted below.\n", "\n", "**Case sensitivity**\n", "\n", "> All variables are case-sensitive. Python treats “number” and “Number” as separate, unrelated entities.\n", "\n", " \n", "**Comments**\n", "> Commented lines start with the hashtag symbol <font color='brown'> # </font> and are ignored when interpreting the code at run-time.\n", "\n", "**Indentation**\n", "> Code blocks are demarcated by their indentation. In particular, **control structures** end with a colon <font color='brown'> : </font> at the decision point. Code blocks indicating action that must be taken when the decision evaluates to \"True\" are demarcated by indentation. \n", ">> Note:  **Spaces and tabs do not mix**\n", "Because whitespace is significant, remember that spaces and tabs don't mix, so use only one or the other when indenting your programs. A common error is to conflate them. While they may look the same during editing, the interpreter will read them differently, and it will result in either an error or unexpected behavior. Most decent text editors can be configured to let the tab key emit spaces instead."]}, {"cell_type": "markdown", "metadata": {}, "source": ["In summary, this section described what a variable is, how you can store information in it, and then retrieve that information at a later stage. The variable can have a name, and this name is usually informed by the kind of content you’ll be storing in the variable. So, if you’re storing your name in the variable, you would name the variable “your<PERSON>ame”. You would not **have** to give it that name, you could name the variable “WowImProgramming”, but that wouldn’t make much sense considering you are trying to store a person’s name. Finally, variables have types, and these types are used to help us organize what can and cannot be stored in the variable. \n", "\n", "> **Hint**: Having a type will help to open up what kinds of things we can do with the information inside the variable. \n", "\n", "\n", "> **Example**: If you have two integers (let’s say 50 and 32), you would be able to subtract one variable from the other (i.e., 50 – 32 = 18). But, if you had two variables that stored names (e.g., <PERSON><PERSON> and <PERSON><PERSON>) it wouldn’t make sense to subtract one from the other (i.e., <PERSON><PERSON>” – <PERSON><PERSON>), because that does not mean anything. \n", "\n", "Therefore, types are a powerful thing, and they help you make sense of what you **can** and cannot do with your variables."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Programming with Python\n", "\n", "Now that the basic concepts have been introduced, let’s make these concrete using illustrative examples. This notebook uses <PERSON><PERSON><PERSON>'s code cells (in light gray background) to demonstrate these ideas. As you learned in the Orientation Module, you can execute instructions in a code cell and the output will be displayed below the cell, where applicable.\n", "\n", "In most cases, a comment is included in the very first line of the cell to help in following the material. A comment is any line in the set of instructions that is preceded by a hashtag symbol. Inside Jupyter, once you have used the comment indicator, the line is highlighted in a different color and the font italicized, which is a very helpful visual aid for both readers and developers. See the example below:\n", "\n", "> **Note**:\n", "\n", "> The code cell below will not produce any output when executed."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# I am a comment and I'm ignored by the interpreter."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.1 Strings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Python works with \"values\" that can take different forms depending on the data type. For example, both a person's first name and age constitute different, valid values inside Python.  "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON>'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# A valid value of type string that can be manipulated.\n", "\"<PERSON>\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON>'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Alternatively, you can also use single quotes to specify the value.\n", "'<PERSON>'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can use the function “``type()``” and include the name of the value in between the parentheses to find out what type the value is. (Functions in Python are explained in more detail later in this notebook.)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["str"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Find the type of a valid value that can be manipulated.\n", "type('Bob')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the above, you can see that our value ```'Bob'``` has type ```str```. The value of a string data type is a sequence of characters. Strings are typically used for text processing.\n", "You can perform concatenation and a number of other functions on strings."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'He is <PERSON>'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# String concatenation using the '+' operator.\n", "'He' + ' ' + 'is' + ' ' + 'Bob'"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# How long is the string '<PERSON>'."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len('<PERSON>')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Print 'Bob' 3 times with no space."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON><PERSON><PERSON><PERSON><PERSON>'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["'<PERSON>'*3"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["'B'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the 1st element of the string '<PERSON>'.\n", "'<PERSON>'[0]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["'o'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the second element of the string '<PERSON>'.\n", "'<PERSON>'[1]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["'b'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the third element of the string '<PERSON>'.\n", "'<PERSON>'[2]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["'b'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the last element of the string '<PERSON>'.\n", "'<PERSON>'[-1]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["'o'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the second last element of the string '<PERSON>'.\n", "'<PERSON>'[-2]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["'BobBobB<PERSON>'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Slicing: Remove the last element of the string '<PERSON><PERSON><PERSON><PERSON><PERSON>' and return the remainder of the string. \n", "('<PERSON>'*3)[0:-1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.2 Integers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Another example of a value is <PERSON>’s age. Assuming <PERSON> is 40 years old, the value can be specified as follows:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["40"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# A valid value of type integer. \n", "40"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["int"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Type of a valid value for <PERSON>'s age.\n", "type(40)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note the difference in how the two values are specified: for string values the name is included between single or double quotes, whereas this is not needed for the age value. Had quotes been used in the latter, it would have expressed a different value of a different type."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["'40'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# A valid value but not of type integer.\n", "'40'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.3 <PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's introduce three new ideas with this example: how to compare two values, another valid data type in Python, and the concept of casting. First, let’s compare the two values (40 and '40') to see if they are of the same type."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["type(40) == type('40')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What is happening here? We have used an operator (```==```) that accepts one value on either side, and compares these values to see if they are the same. The values in this case are not ```40``` and ```'40'```, but are their value types (i.e., integer, string, etc.). (If our intention is to compare the original values we would have used ```40 == '40'```)\n", "\n", "What type is the resulting False value?"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["bool"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# Type of value from a comparison.\n", "type(type(40) == type('40'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The \"False\" value is of the “Boolean” type, which is another fundamental data type in programming. Boolean values can only take two values: \"True\" or \"False\".\n", "\n", "It is often the case that, instead of working with the native data type, such as the Boolean data type that you have just seen, you want to use a different but equivalent data type. For example, if you accept that a \"False\" value represents the lack of something in a given context, you may want to represent that as an integer with the value 0. This is achieved using the concept of **casting**, which allows certain data types to be cast as a different type. In Python, you cast a value by calling the type that you want to cast it to, and including the value as an argument to that call. Thus, in this case, you achieve the following."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Casting a bool to an int.\n", "int(type(40) == type('40'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.4 Floating point numbers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Floating point numbers are numbers that contain floating decimal points."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["2.6"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# Example of a floating number.\n", "2.6"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["float"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# Find the type of a floating number.\n", "type(2.6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Remember that data types define what operations are allowed between values of the same type. However, certain data types, such as integers and floating numbers, can be used in expressions. In this case, casting occurs without the need for the user to do it manually. The resulting value takes the type of the more encompassing data type of the involved values, as can be seen in the example below: "]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["42.6"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# Adding an integer to a float results in a value of type float.\n", "40 + 2.6"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["float"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["type(40 + 2.6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What happens when dividing two integer values? "]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.275"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# Divide two integers.\n", "11 / 40"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["float"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# Type of resulting value from dividing two integers.\n", "type(11 / 40)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-warning\">\n", "<b>Note for Python 2.7 users</b>:<br>\n", "It turns out that, in Python 2.7 and earlier versions, operations involving two integers will result in an integer value, even if the correct result is a float. Thus, one needs to be very careful when dealing with integer operations. Ideally, you always want to ensure that you cast one of the values (it does not matter which one) to a float, so that the resulting value is correctly represented as a float. This is not required for Python 3 and later.\n", "</div>"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# Example of casting integer prior to performing math operations required for Python 2.7 and earlier.\n", "# 11/float(40)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.5 Complex numbers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Complex numbers are important in many scientific and engineering disciplines. Python can also represent and perform calculations with  [complex numbers](https://docs.python.org/3/library/cmath.html)."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1.5+2j)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# Complex numbers are represented by a real + imaginary part. The imaginary part is indicated by adding a suffix j.\n", "1.5 + 2j"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["complex"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["type((1.5 + 2j))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.6 The assignment operator and variables"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Our expressions, for example \"```type(40) == type('40')```\", can quickly become unwieldy and a source of errors that are difficult to debug. To help in manipulating values elegantly (and programmatically), it is common to use variables to store the value of interest. The value is then referred to by referring to the variable. To assign a value to a variable, we use the assignment operator \"```=```\" as in the following example:"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["a_boolean_value = (type(40) == type('40'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here, the value on the right-hand side of the assignment operator “```=```” has been assigned to the variable that has been cleverly named “```a_boolean_value```”. You can now use the variable in other parts of your code.\n", "\n", "Let’s introduce another function, called “print” (another **keyword**), which prints the value associated with your variable."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["print(a_boolean_value)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use print for other expressions you have met before:"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.275\n"]}], "source": ["print(11/float(40))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The print function allows you to perform other formatting and interesting value referencing."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> is 40 years old and spent 11 in school, that is 27.5% of his life.\n"]}], "source": ["print('{} is {} years old and spent {} in school, that is {}% of his life.'.format('<PERSON>',40, 11,100*float(11)/40))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this print statement, the ```str.format()``` form has been used as argument, where “placeholders” or format fields indicated by ```{}```, are replaced by arguments specified in format argument. The arguments are used in the order used inside the parentheses, and must be equal to the number of the ```{}``` in the ```str``` part. You can also specify which positional argument should go where by including numbering in the format fields."]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> is 40 years old and spent 11 in school, that is 27.5% of his life.\n"]}], "source": ["print('{0} is {1} years old and spent {2} in school, that is {3}% of his life.'.format('<PERSON>',40, 11,100*float(11)/40))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By using numbered fields, the arguments are used depending on which number they are, with the first argument in format numbered as 0.\n", "Using print in this way is especially useful when you want to combine different data types in your print statement as illustrated above. You can also control how the formatting should be done instead of relying on Python's default behavior. For example, if you don't care about the decimal in the percentage, you can call print as follows:"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> is 40 years old and spent 11 in school, that is 28% of his life.\n"]}], "source": ["print('{0} is {1} years old and spent {2} in school, that is {3:.0f}% of his life.'.format('<PERSON>',40,11,100*float(11)/40))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Notice the value has only been rounded in the print output but not its representation in memory."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>\n", "<div class=\"alert alert-info\">\n", "<b>Exercise 1 Start: Printing strings.</b>\n", "</div>\n", "\n", "### Instructions\n", "1. Set your first and last names as separate variables (\"firstname\" and \"lastname\", respectively).\n", "2. Print your first name and last name using the \"``str.format()``\" form discussed above. Include a whitespace character between your first and last name in the print output."]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n"]}], "source": ["firstname = '<PERSON><PERSON>'\n", "lastname = 'La Torre'\n", "print('{} {}' .format(firstname,lastname))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>\n", "<div class=\"alert alert-info\">\n", "<b>Exercise 1 End.</b>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> **Exercise complete**:\n", "    \n", "> This is a good time to \"Save and Checkpoint\"."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.7 Aggregate data types: lists and tuples\n", "\n", "Variables store information that may change over time. When you need to store longer lists of information, there are additional options available. You will be introduced to lists and tuples as basic elements, and can read more about native Python data structures in the [Python documentation](https://docs.python.org/3/tutorial/datastructures.html). During this course, you will be exposed to examples using the [Pandas](http://pandas.pydata.org/) and [NumPy](http://www.numpy.org/) libraries, which offer additional options for data analysis and manipulation.\n", "\n", "### 2.7.1 Lists\n", "[Lists](https://docs.python.org/3/tutorial/introduction.html#lists) are changeable sequences of values. They are stored within square brackets, and items within a list are separated by commas. Lists can also be modified, appended, and sorted, among other [methods](https://docs.python.org/3/tutorial/datastructures.html#more-on-lists)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Create and print a list"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['this', 'is', 'a', 'list']\n", "The list contains 4 elements.\n"]}], "source": ["# Lists.\n", "lst = ['this', 'is', 'a', 'list']\n", "print(lst)\n", "\n", "# Print the number of elements in the list.\n", "print('The list contains {} elements.'.format(len(lst)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Selecting an element in a list\n", "Remember that Python uses zero indexing, that is, the index starts at zero. You can use a negative index to select the last element in the list."]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["this\n", "a\n", "list\n"]}], "source": ["# Print the first element in the list.\n", "print(lst[0])\n", "\n", "# Print the third element in the list.\n", "print(lst[2])\n", "\n", "# Print the last element in the list.\n", "print(lst[-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Append to a list"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['this', 'is', 'a', 'list']\n", "['this', 'is', 'a', 'list', 'with', 'appended', 'elements']\n", "The updated list contains 7 elements.\n"]}], "source": ["# Appending a list.\n", "print(lst)\n", "lst.append('with')\n", "lst.append('appended')\n", "lst.append('elements')\n", "print(lst)\n", "\n", "# Print the number of elements in the list.\n", "print('The updated list contains {} elements.'.format(len(lst)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> **Note**:\n", "\n", "> When selecting and executing the cell again, you will continue to add values to the list.\n", "\n", "> **Try this**: Select the cell above again and execute it to see how the input and output content changes.\n", "\n", "> This can come in handy when working with loops."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Changing a list**\n", "\n", "This course will not cover string methods in detail. You can read more about [string methods](https://docs.python.org/3/library/stdtypes.html?highlight=upper#sequence-types-list-tuple-range) in the Python documentation, if you are interested."]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['THIS', 'is', 'a', 'LIST', 'with', 'appended', 'elements']\n"]}], "source": ["# Changing a list.\n", "# Note: Remember that Python starts with index 0.\n", "lst[0] = 'THIS'\n", "lst[3] = lst[3].upper()\n", "print(lst)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Define a list of numbers"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 10, 2, 7, 8, 5, 6, 3, 4, 1, 9]\n"]}], "source": ["# Define a list of numbers.\n", "numlist = [0, 10, 2, 7, 8, 5, 6, 3, 4, 1, 9]\n", "print(numlist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Sort and filter a list"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5, 6, 7, 8, 9, 10]\n"]}], "source": ["# Sort and filter list.\n", "sorted_and_filtered_numlist = sorted(i for i in numlist if i >= 5)\n", "print(sorted_and_filtered_numlist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Remove the last element from a list"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5, 6, 7, 8, 9]\n"]}], "source": ["# Remove the last element from the list.\n", "list.pop(sorted_and_filtered_numlist)\n", "print(sorted_and_filtered_numlist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.7.2 <PERSON><PERSON>\n", "[Tuples](https://docs.python.org/3/tutorial/datastructures.html?highlight=tuples#tuples-and-sequences) are similar to lists, except that they are defined in parentheses and are unchangeable, which means that their values cannot be modified."]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('this', 'is', 'a', 'bigger', 'tuple')\n"]}], "source": ["tup = ('this', 'is', 'a', 'bigger', 'tuple')\n", "print(tup)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["'bigger'"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["tup[3]"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'tuple' object does not support item assignment", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "\u001b[1;32mC:\\Users\\<USER>\\AppData\\Local\\Temp/ipykernel_27164/2444582927.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;31m# Tuples cannot be changed and will fail with an error if you try to change an element.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[0mtup\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;36m3\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;34m'new'\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;31mTypeError\u001b[0m: 'tuple' object does not support item assignment"]}], "source": ["# Tuples cannot be changed and will fail with an error if you try to change an element.\n", "tup[3] = 'new'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.8 Loops, sequences, and conditionals\n", "Conditions and loops are control structures that can be used to repeat statements based on the conditions or loops specified. This can be employed to cycle through data sets or perform a sequence of steps on (or based on) input variables."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.9 Range\n", "[Range](https://docs.python.org/3/library/stdtypes.html#range) (start, stop, step) is used to create lists containing arithmetic progressions. If you call a range with only one argument specified, it will use the value as the stop value and default to zero as the start value. The step argument is optional and can be a positive or negative integer."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate a list of 10 values.\n", "myrange = list(range(10))\n", "myrange"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate a list with start value equal to one, stop value equal to ten that increments by three.\n", "myrange2 = list(range(1, 10, 3))\n", "myrange2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate a negative list.\n", "myrange3 = list(range(0, -10, -1))\n", "myrange3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.10 Basic loops\n", "Python uses indentation to repeat items. The example below demonstrates how to do the following:\n", "- Cycle through the generated list and:\n", "  - Print the current element in the list; and\n", "  - Print Xs, which are repeated per element.\n", "- Exit the loop and print a line that is not repeated."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# You can specify the list manually or use a variable containing the list as input.\n", "# The syntax for manual input is: `for item in [1, 2, 3]:`\n", "\n", "for item in myrange:\n", "    print(item)\n", "    print(item * 'X')\n", "print('End of loop (not included in the loop)')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.11 Conditionals\n", "Conditionals are used to determine which statements are executed. In the example below, we import the \"random\" library and generate a random number smaller than 2. Assume 0 means heads and 1 means tails. The conditional statement is then used to print the result of the coin flip, as well as \"Heads\" or \"Tails\"."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Flip a coin.\n", "\n", "import random\n", "coin_result = random.randrange(0,2)\n", "\n", "print(coin_result)\n", "\n", "if coin_result == 0:\n", "    print('Heads')\n", "else:\n", "    print('Tails')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.11.1 Importing modules:\n", "Code reuse is an integral component in programming, and is critical for extending basic functionality of an underlying programmming environment. In Python, modules exists that contain functions, classes, variables and other code that we can reuse in our own code. In most cases, these modules are provided under permissive free software license such as the MIT license. Assuming you know the name of the module you want to use, there are a number of ways you can tell Python that you want to access that module.\n", "- ```import X ```\n", "> This imports the module X, and creates a reference to that module in the current namespace. After running this statement, you can use X.name to refer to things defined in module X. In the above example, we import the **random** module, and used the ```random.randrange(0,2)``` to call a function called ```randrange``` in that module. It is also common to use an alias for ``X`` by importing the module as ```import X as alias```. Now we use ```alias.name``` to refer things defined in module X. \n", "\n", "- ```from X import * ```\n", "> This statement imports the module X, and creates references in the current namespace to all public objects defined by that module (that is, everything that doesn’t have a name starting with \"``_``\"). After you’ve run this statement, you can simply use a name to refer to things defined in module X without prepending ``X.`` to it. Although there are cases where this is necessary, it is best to avoid this in the majority of cases as it can lead to unexpected behaviour.\n", "\n", "- ``from X import a, b, c`` \n", "> This works like the previous statement by importing the module X, but creates references in the current namespace only to the objects provided (implying you should know that these are defined in the module). Thus, you can now use ``a`` and ``b`` and ``c`` in your program."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.12 Functions\n", "Functions allow for code reuse within and across applications. When defining a function, you would use the “```def```” statement. The desired function code is then placed into the body of the statement. A function usually takes in an argument and returns a value as determined by the code in the body of the function. Whenever referring to the function, outside of the function body itself, this action is known as a function call."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function 'myfirstfunction' with argument 'x'.\n", "def myfirstfunction(x):\n", "    y = x * 6\n", "    return y\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can now call the function as in the next example."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Call your function.   \n", "z = myfirstfunction(6)\n", "print(z)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Function definitions, loops, and conditionals can be combined to produce something useful. The example below will simulate a variable number of coin flips and then produce the summary of results as output."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "random.random()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def coinflip(total_number_of_flips):\n", "    '''\n", "    function 'coinflip' with argument 'total_number_of_flips' for the number of repetitions \n", "    that returns the number of 'tail' occurrences\n", "    '''\n", "    # Set all starting variables to 0.\n", "    heads = 0\n", "    tails = 0\n", "    current_number_of_flips = 0\n", "    # Start a loop that executes statements while the conditional specified results in 'True'.\n", "    while current_number_of_flips < total_number_of_flips:\n", "        # Generate a random number smaller than 2.\n", "        current_flip = random.randrange(0,2)\n", "        # Increment heads by 1 if the generated number is 0.\n", "        if current_flip == 0:\n", "            heads = heads + 1\n", "        # Increment tails by 1 if the generated number is larger than 0.\n", "        if current_flip == 1:\n", "            tails = tails + 1\n", "        # Increment the flip variable by 1.\n", "        current_number_of_flips += 1\n", "    return [heads, tails]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the above function definition, there is a “docstring” to describe what the function does in lieu of the comment that has been used. This is considered best practice when defining functions in Python. The docstring is included just after the ```def``` statement, and is included between a pair of three single or double quotation marks.\n", "\n", "In the \"```coinflip```\" function, you are simulating the number of times an unbiased coin returns heads or tails on being flipped. To track these values, initialize the counts to zero for both the heads and tails variables. Moreover, it is also important to include a variable flip that tracks the number of flips in the simulation. The simulation ends when the number of flips required, as specified in the function argument, is reached. To simulate the actual coin flip or flip event, call the function “```randrange```” from the Python \"```random```\" module, which accepts a minimum of two parameters: the start of the range from which you want to simulate, and the end of the range (stop) from which point and above you do not care about. You can see what this \"``randrange``\" function does by calling it for the given parameters."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime \n", "now = datetime.datetime.now() \n", "random.seed(now)\n", "for k in range(0,10):\n", "    print(random.randrange(0,2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Every time you call \"```randrange```\", either a 0 or 1 is returned with equal probability. Hence, it is a good approximation tp flipping an unbiased coin in our example. This example also does something that is slightly complex to describe. Before entering the loop, set your random seed generator to the current time. This ensures you do not always get the same sequence, as random number generation inside the program is not truly random, but follows a deterministic algorithm. \n", "> **Note:**\n", ">\n", "> Later modules will exploit this determinism by setting the random seed for reproducibility of expected outcomes.\n", "\n", "Going back to the coin flip function, when you flip the coin, a 0 or 1 is obtained as a value. You check whether this is 0, and if so, you increment your count of heads by 1. If it is a 1, you increment the count of tails by 1. Recall that these two states are mutually exclusive, and only one can occur on each flip. After these checks, you also increase your count of flips tracked in “```current_number_of_flips```”. You repeat this until you have reached the “```total_number_of_flips```”.\n", "\n", "Finally, the function returns the counts of both heads and tails obtained from your simulation as an array, where the first entry is number of heads. Let’s invoke the function for 100 flips."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set the number of repetitions.\n", "num_flips = 100\n", "\n", "# Call the function and set the output to the variable 'tails'.\n", "coinflip_results = coinflip(num_flips)\n", "\n", "# Output the values returned.\n", "print(coinflip_results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Is this coin balanced? You would need to run more simulations for a more definitive answer. Try 10000 simulations. Since you have a function already written, you can simply reuse it, as long as you specify a different value for the argument. This is the power of writing functions for tasks that follow the same design pattern."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_flips = 10000\n", "coinflip_results = coinflip(num_flips)\n", "print('Heads returned: {:.0f}%'.format(100*float(coinflip_results[0])/sum(coinflip_results)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Notice how this example has used a number of the ideas introduced in this notebook in computing the proportion of heads."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>\n", "<div class=\"alert alert-info\">\n", "<b>Exercise 2 Start: Rolling Dice.</b>\n", "</div>\n", "\n", "### Instructions\n", "\n", "> Use the \"coinflip\" example above and change it to simulate rolling dice. Your output should contain summary statistics for the number of times you rolled the dice, and occurrences of each of the 6 sides.\n", "\n", "> **Hints**:\n", "\n", "> - Replace \"Heads\" and \"Tails\" with six new variables: \"Side_1\", \"Side_2\", “Side_3”, etc.\n", "> - Replace ```random.randrange(0,2)``` with ```random.randrange(1,7)```\n", "> - Test for whether each state of the random variable (which side comes up after a die throw), and increase the counter for the relevant variable.\n", "> - Return final states of each variable as a vector (i.e., [Side_1, Side_2, Side_3, etc.])."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "Side_1 = 0\n", "Side_2 = 0\n", "Side_3 = 0\n", "Side_4 = 0\n", "Side_5 = 0\n", "Side_6 = 0\n", "current_number_of_flips = 0\n", "total_number_of_flips = 60000\n", "while current_number_of_flips < total_number_of_flips:\n", "    current_flip = random.randrange(1,7)\n", "    if current_flip == 1:\n", "        Side_1 = Side_1 + 1\n", "    if current_flip == 2:\n", "        Side_2 = Side_2 + 1\n", "    if current_flip == 3:\n", "        Side_3 = Side_3 + 1\n", "    if current_flip == 4:\n", "        Side_4 = Side_4 + 1\n", "    if current_flip == 5:\n", "        Side_5 = Side_5 + 1\n", "    if current_flip == 2:\n", "        Side_6 = Side_6 + 1\n", "    current_number_of_flips += 1\n", "print(Side_1,Side_2,Side_3,Side_4,Side_5,Side_6) "]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["> **Exercise complete**:\n", "    \n", "> This is a good time to \"Save and Checkpoint\"."]}, {"cell_type": "markdown", "metadata": {}, "source": ["The basic random function was used in the example above, but you can visit Big Data Examiner to see an example of [implementing other probability distributions](http://bigdata-madesimple.com/how-to-implement-these-5-powerful-probability-distributions-in-python/)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. Graphics and visualization\n", "\n", "Matplotlib is the standard library commonly used to display graphics and plots in Python. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.1 Matplotlib\n", "\n", "The examples below come from the [Matplotlib Screenshots](http://matplotlib.org/users/screenshots.html) page.\n", "\n", "> **Note**: \n", "\n", "> One of the options that you will need to remember to set when you use Matplotlib is \"%matplotlib inline\", which instructs the notebook to plot inline instead of opening a separate window for your graph.\n", "\n", "You can find additional information on Matplotlib through the following resources:\n", "- [Matplotlib Documentation](http://matplotlib.org/)\n", "\n", "- [Matplotlib Beginners](http://matplotlib.org/users/beginner.html)\n", "\n", "- [Matplotlib Gallery](http://matplotlib.org/gallery.html)"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["### 3.1.1 Simple plot\n", "The graph below does not contain any real significance other than demonstrating how easy it is to plot graphs. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import matplotlib library and set notebook plotting options.\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "# Instruct the notebook to plot inline. \n", "%matplotlib inline     \n", "plt.rcParams['figure.figsize'] = (15, 9) \n", "plt.rcParams['axes.titlesize'] = 'large'\n", "\n", "# Generate data.\n", "t = np.arange(0.0, 2.0, 0.01)\n", "s = np.sin(2*np.pi*t)\n", "\n", "# Create plot.\n", "plt.plot(t, s)\n", "\n", "# Set plot options.\n", "plt.xlabel('time (s)')\n", "plt.ylabel('voltage (mV)')\n", "plt.title('About as simple as it gets, folks')\n", "plt.grid(True)\n", "\n", "# Saving as file can be achieved by uncommenting the line below.\n", "# plt.savefig(\"test.png\")\n", "\n", "# Display the plot in the notebook.\n", "# The '%matplotlib inline' option set earlier ensure that the plot is displayed inline.\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1.2 Subplots\n", "\"Subplots\" is another useful feature where you can display multiple plots. This is typically used to visually compare data sets."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Simple demo with multiple subplots.\n", "\"\"\"\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "x1 = np.linspace(0.0, 10.0)\n", "x2 = np.linspace(0.0, 2.0)\n", "\n", "y1 = np.cos(2 * np.pi * x1) * np.exp(-x1)\n", "y2 = np.cos(2 * np.pi * x2)\n", "\n", "plt.subplot(2, 1, 1)\n", "plt.plot(x1, y1, 'yo-')\n", "plt.title('A tale of 2 subplots')\n", "plt.ylabel('Damped oscillation')\n", "\n", "plt.subplot(2, 1, 2)\n", "plt.plot(x2, y2, 'r.-')\n", "plt.xlabel('time (s)')\n", "plt.ylabel('Undamped')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1.3 Histogram \n", "Here is another example of a plot that will be utilized in future modules. The example demonstrates syntax, and provides ideas of what is possible."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Demo of the histogram (hist) function with a few features.\n", "\n", "In addition to the basic histogram, this demo shows a few optional features:\n", "\n", "    * Setting the number of data bins\n", "    * The ``normed`` flag, which normalizes bin heights so that the integral of\n", "      the histogram is 1. The resulting histogram is a probability density.\n", "    * Setting the face color of the bars\n", "    * Setting the opacity (alpha value).\n", "\n", "\"\"\"\n", "import numpy as np\n", "import matplotlib.mlab as mlab\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "# Example data.\n", "mu = 100  # Mean of distribution.\n", "sigma = 15  # Standard deviation of distribution.\n", "x = mu + sigma * np.random.randn(10000)\n", "\n", "num_bins = 50\n", "# The histogram of the data.\n", "n, bins, patches = plt.hist(x, num_bins, normed=1, facecolor='green', alpha=0.5)\n", "# Add a 'best fit' line.\n", "y = mlab.normpdf(bins, mu, sigma)\n", "plt.plot(bins, y, 'r--')\n", "plt.xlabel('Smarts')\n", "plt.ylabel('Probability')\n", "plt.title(r'Histogram of IQ: $\\mu=100$, $\\sigma=15$')\n", "\n", "# Tweak spacing to prevent clipping of ylabel.\n", "plt.subplots_adjust(left=0.15)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1.4 Visualizing coin flip distribution\n", "\n", "This is a continuation of your coin flip simulation from the previous section. Although you already have a fairly accurate idea of whether or not the coin is biased, in statistics, the typical approach requires sampling from the underlying distribution multiple times, and then plotting the sampling distribution. To this end, a handle is required on the actual value for the probability of landing heads between 0 and 1 (probability of landing tails will then be 1 minus that value), which can be generated using the \"```random()```\" method from the **random** module, that is the call \"```random.random()```\". In the case of a fair coin, designate as heads anytime the value generated is below 0.5, otherwise it is assigned as tails. An unfair coin can be simulated by adjusting this threshold; the closer the value is to 0 (and below 0.5) the less likely the chance of getting heads in multiple trials. This is an involved complex statistical procedure, and is captured here for illustrative purposes. \n", "\n", "In the next code cell, we simulate flipping a fair coin by specifying the threshold as 0.5. You will flip the coin 10 times and note the number of times it falls heads in those trials. This is repeated 1000 times, from which a plot of the sampling distribution of the underlying distribution (known as the binomial distribution) is generated."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["threshold = 0.5\n", "ntrials = 10 # Flips per trial.\n", "size = 1000 # Repetitions.\n", "M = [0 for x in range(0,size)] \n", "for i in range(0,size):\n", "    M[i] = sum([random.random()< threshold for x in range(0,ntrials)])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Cross tabulation for frequency of occurence of values.\n", "M_xtab = [0 for i in range(0, ntrials)]\n", "for i in range(0,ntrials):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(M_xtab)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# The histogram of the data.\n", "plt.hist(M, range=(0,ntrials),bins=ntrials)\n", "plt.xlabel('Number  of heads')\n", "plt.ylabel('Frequency')\n", "plt.title('Coin flip distribution')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As before, the chance of our coin landing heads is as one would expect from an unbiased coin."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>\n", "<div class=\"alert alert-info\">\n", "<b>Exercise 3 Start: Coin flip distribution.</b>\n", "</div>\n", "\n", "### Instructions\n", "\n", "> Using the \"coinflip\" example above, simulate the distribution one would get for a biased coin that has an *80%* probability of landing tails for *1000* simulations, where the coin is flipped *20* times at each simulation. Plot the histogram using the Matplotlib.\n", "\n", "> **Hints**:\n", "\n", "> - Change \"ntrials\", \"size\", and \"threshold\" in the coin simulation code from above.\n", "> - The probability of getting tails is given. You need to compute the probability of getting heads from this value, which you will then use as the threshold value."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["threshold = 0.2\n", "ntrials = 20 # Flips per trial.\n", "size = 1000 # Repetitions.\n", "M = [0 for x in range(0,size)] \n", "for i in range(0,size):\n", "    M[i] = sum([random.random()< threshold for x in range(0,ntrials)])\n", "M_xtab = [0 for i in range(0, ntrials)]\n", "for i in range(0,ntrials):\n", "    M_xtab[i] = sum([x == i for x in M])\n", "plt.hist(M, range=(0,ntrials),bins=ntrials)\n", "plt.xlabel('Number  of heads')\n", "plt.ylabel('Frequency')\n", "plt.title('Coin flip distribution')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<br>\n", "<div class=\"alert alert-info\">\n", "<b>Exercise 3 End.</b>\n", "</div>\n", "\n"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["# 4. Save your notebook\n", "\n", "Please make sure that you:\n", "- Perform a final \"Save and Checkpoint\";\n", "- Download a copy of the notebook in \".ipynb\" format to your local machine using \"File\", \"Download as\", and \"IPython Notebook (.ipynb)\"."]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["# 5. Additional resources\n", "\n", "> **Note**:\n", "\n", "> This resource is optional and is only included for students who wish to complete additional exercises in Python.\n", "\n", "- Additional [Python course](http://www.python-course.eu/course.php).\n", "- [Python tips for beginners](http://www.techbeamers.com/top-10-python-coding-tips-for-beginners/)\n", "- [30 essential Python tips and tricks](http://www.techbeamers.com/essential-python-tips-tricks-programmers/)\n", "\n", "    "]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["# 6. References\n", "Python Software Foundation. 2016. “General Python FAQ - Python 3.6.2 documentation.”Last accessed August 20, 2017. https://docs.python.org/3/faq/general.html#what-is-python. \n", "\n", "MIT OpenCourseWare. 2011. “6.096 Introduction to C++.” Accessed September 20. https://ocw.mit.edu/courses/electrical-engineering-and-computer-science/6-096-introduction-to-c-january-iap-2011/lecture-notes/MIT6_096IAP11_lec02.pdf. "]}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 1}